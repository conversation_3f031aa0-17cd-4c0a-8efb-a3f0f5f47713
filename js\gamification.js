// Gamification System
class GamificationSystem {
    constructor() {
        this.achievements = this.initializeAchievements();
        this.streakData = this.loadStreakData();
        this.quizStats = this.loadQuizStats();
        this.bindEvents();
    }

    // Initialize achievement definitions
    initializeAchievements() {
        return {
            first_lesson: {
                id: 'first_lesson',
                title: 'الخطوة الأولى',
                description: 'أكمل أول درس لك',
                icon: 'fas fa-baby',
                points: 100,
                type: 'lesson_count',
                requirement: 1
            },
            early_bird: {
                id: 'early_bird',
                title: 'الطائر المبكر',
                description: 'ابدأ التعلم قبل الساعة 9 صباحاً',
                icon: 'fas fa-sun',
                points: 150,
                type: 'time_based',
                requirement: 'before_9am'
            },
            quiz_master: {
                id: 'quiz_master',
                title: 'سيد الاختبارات',
                description: 'احصل على 100% في اختبار',
                icon: 'fas fa-medal',
                points: 200,
                type: 'quiz_perfect',
                requirement: 1
            },
            streak_3: {
                id: 'streak_3',
                title: 'المثابر',
                description: 'تعلم لمدة 3 أيام متتالية',
                icon: 'fas fa-fire',
                points: 250,
                type: 'streak',
                requirement: 3
            },
            streak_7: {
                id: 'streak_7',
                title: 'سيد الاستمرارية',
                description: 'تعلم لمدة 7 أيام متتالية',
                icon: 'fas fa-fire',
                points: 500,
                type: 'streak',
                requirement: 7
            },
            module_master: {
                id: 'module_master',
                title: 'سيد الوحدة',
                description: 'أكمل وحدة كاملة',
                icon: 'fas fa-graduation-cap',
                points: 300,
                type: 'module_complete',
                requirement: 1
            },
            point_collector_500: {
                id: 'point_collector_500',
                title: 'جامع النقاط',
                description: 'احصل على 500 نقطة',
                icon: 'fas fa-star',
                points: 100,
                type: 'points',
                requirement: 500
            },
            point_collector_1000: {
                id: 'point_collector_1000',
                title: 'جامع النقاط المتقدم',
                description: 'احصل على 1000 نقطة',
                icon: 'fas fa-star',
                points: 200,
                type: 'points',
                requirement: 1000
            },
            speed_learner: {
                id: 'speed_learner',
                title: 'المتعلم السريع',
                description: 'أكمل 5 دروس في يوم واحد',
                icon: 'fas fa-bolt',
                points: 300,
                type: 'daily_lessons',
                requirement: 5
            },
            marketing_expert: {
                id: 'marketing_expert',
                title: 'خبير التسويق',
                description: 'أكمل جميع الوحدات',
                icon: 'fas fa-crown',
                points: 1000,
                type: 'all_modules',
                requirement: 'complete_all'
            }
        };
    }

    // Load streak data from localStorage
    loadStreakData() {
        const saved = localStorage.getItem('learningStreak');
        if (saved) {
            try {
                return JSON.parse(saved);
            } catch (e) {
                console.error('خطأ في تحميل بيانات الاستمرارية:', e);
            }
        }
        return {
            currentStreak: 0,
            longestStreak: 0,
            lastActivity: null,
            dailyLessons: {}
        };
    }

    // Load quiz statistics
    loadQuizStats() {
        const saved = localStorage.getItem('quizStats');
        if (saved) {
            try {
                return JSON.parse(saved);
            } catch (e) {
                console.error('خطأ في تحميل إحصائيات الاختبارات:', e);
            }
        }
        return {
            totalQuizzes: 0,
            perfectScores: 0,
            averageScore: 0,
            quizHistory: []
        };
    }

    // Save streak data
    saveStreakData() {
        try {
            localStorage.setItem('learningStreak', JSON.stringify(this.streakData));
        } catch (e) {
            console.error('خطأ في حفظ بيانات الاستمرارية:', e);
        }
    }

    // Save quiz statistics
    saveQuizStats() {
        try {
            localStorage.setItem('quizStats', JSON.stringify(this.quizStats));
        } catch (e) {
            console.error('خطأ في حفظ إحصائيات الاختبارات:', e);
        }
    }

    // Update streak when user completes a lesson
    updateStreak() {
        const today = new Date().toDateString();
        const yesterday = new Date(Date.now() - 86400000).toDateString();
        const lastActivity = this.streakData.lastActivity;

        if (lastActivity === today) {
            // Already learned today, just update daily lesson count
            this.updateDailyLessons();
            return;
        }

        if (lastActivity === yesterday) {
            // Continuing streak
            this.streakData.currentStreak++;
        } else if (lastActivity === null || lastActivity !== yesterday) {
            // Starting new streak
            this.streakData.currentStreak = 1;
        }

        // Update longest streak
        if (this.streakData.currentStreak > this.streakData.longestStreak) {
            this.streakData.longestStreak = this.streakData.currentStreak;
        }

        this.streakData.lastActivity = today;
        this.updateDailyLessons();
        this.saveStreakData();

        // Check for streak achievements
        this.checkStreakAchievements();
    }

    // Update daily lesson count
    updateDailyLessons() {
        const today = new Date().toDateString();
        if (!this.streakData.dailyLessons[today]) {
            this.streakData.dailyLessons[today] = 0;
        }
        this.streakData.dailyLessons[today]++;

        // Check for daily lesson achievements
        this.checkDailyLessonAchievements();
    }

    // Check streak-based achievements
    checkStreakAchievements() {
        const currentStreak = this.streakData.currentStreak;
        
        if (currentStreak >= 3 && !this.isAchievementUnlocked('streak_3')) {
            this.unlockAchievement('streak_3');
        }
        
        if (currentStreak >= 7 && !this.isAchievementUnlocked('streak_7')) {
            this.unlockAchievement('streak_7');
        }
    }

    // Check daily lesson achievements
    checkDailyLessonAchievements() {
        const today = new Date().toDateString();
        const todayLessons = this.streakData.dailyLessons[today] || 0;
        
        if (todayLessons >= 5 && !this.isAchievementUnlocked('speed_learner')) {
            this.unlockAchievement('speed_learner');
        }
    }

    // Record quiz completion
    recordQuizCompletion(score, maxScore) {
        const percentage = (score / maxScore) * 100;
        
        this.quizStats.totalQuizzes++;
        this.quizStats.quizHistory.push({
            score: percentage,
            date: new Date().toISOString(),
            perfect: percentage === 100
        });

        if (percentage === 100) {
            this.quizStats.perfectScores++;
            if (!this.isAchievementUnlocked('quiz_master')) {
                this.unlockAchievement('quiz_master');
            }
        }

        // Calculate new average
        const totalScore = this.quizStats.quizHistory.reduce((sum, quiz) => sum + quiz.score, 0);
        this.quizStats.averageScore = totalScore / this.quizStats.totalQuizzes;

        this.saveQuizStats();
        return percentage;
    }

    // Check if achievement is unlocked
    isAchievementUnlocked(achievementId) {
        return window.userProgress.achievements.includes(achievementId);
    }

    // Unlock achievement
    unlockAchievement(achievementId) {
        if (this.isAchievementUnlocked(achievementId)) {
            return false;
        }

        const achievement = this.achievements[achievementId];
        if (!achievement) {
            console.error('إنجاز غير موجود:', achievementId);
            return false;
        }

        // Add to user progress
        window.userProgress.achievements.push(achievementId);
        
        // Add bonus points
        window.userProgress.points += achievement.points;
        
        // Save progress
        saveUserProgress();
        
        // Show notification
        this.showAchievementNotification(achievement);
        
        // Update UI
        updateProgressDisplay();
        
        return true;
    }

    // Show achievement notification
    showAchievementNotification(achievement) {
        const notification = document.createElement('div');
        notification.className = 'achievement-notification';
        notification.innerHTML = `
            <div class="achievement-notification-content">
                <div class="achievement-notification-icon">
                    <i class="${achievement.icon}"></i>
                </div>
                <div class="achievement-notification-text">
                    <div class="achievement-notification-title">إنجاز جديد!</div>
                    <div class="achievement-notification-name">${achievement.title}</div>
                    <div class="achievement-notification-points">+${achievement.points} نقطة</div>
                </div>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: linear-gradient(135deg, var(--accent-color), #d97706);
            color: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            animation: slideInRight 0.5s ease-out;
            max-width: 300px;
            direction: rtl;
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.5s ease-in';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 500);
        }, 5000);

        // Add click to dismiss
        notification.addEventListener('click', () => {
            notification.style.animation = 'slideOutRight 0.5s ease-in';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 500);
        });
    }

    // Check all achievements based on current progress
    checkAllAchievements() {
        const progress = window.userProgress;
        
        // Lesson count achievements
        const totalLessons = getTotalLessonsCompleted();
        if (totalLessons >= 1 && !this.isAchievementUnlocked('first_lesson')) {
            this.unlockAchievement('first_lesson');
        }

        // Points achievements
        if (progress.points >= 500 && !this.isAchievementUnlocked('point_collector_500')) {
            this.unlockAchievement('point_collector_500');
        }
        if (progress.points >= 1000 && !this.isAchievementUnlocked('point_collector_1000')) {
            this.unlockAchievement('point_collector_1000');
        }

        // Module completion achievements
        if (hasCompletedAnyModule() && !this.isAchievementUnlocked('module_master')) {
            this.unlockAchievement('module_master');
        }

        // All modules achievement
        const allModulesComplete = Object.values(progress.modules).every(module => 
            module.completed === module.total
        );
        if (allModulesComplete && !this.isAchievementUnlocked('marketing_expert')) {
            this.unlockAchievement('marketing_expert');
        }

        // Time-based achievement (early bird)
        const currentHour = new Date().getHours();
        if (currentHour < 9 && !this.isAchievementUnlocked('early_bird')) {
            this.unlockAchievement('early_bird');
        }
    }

    // Bind events
    bindEvents() {
        // Listen for lesson completions
        document.addEventListener('lessonCompleted', () => {
            this.updateStreak();
            this.checkAllAchievements();
        });

        // Listen for quiz completions
        document.addEventListener('quizCompleted', (event) => {
            const { score, maxScore } = event.detail;
            this.recordQuizCompletion(score, maxScore);
        });
    }

    // Get streak display info
    getStreakInfo() {
        return {
            current: this.streakData.currentStreak,
            longest: this.streakData.longestStreak,
            todayLessons: this.streakData.dailyLessons[new Date().toDateString()] || 0
        };
    }

    // Get quiz statistics
    getQuizStatistics() {
        return {
            total: this.quizStats.totalQuizzes,
            perfect: this.quizStats.perfectScores,
            average: Math.round(this.quizStats.averageScore),
            perfectRate: this.quizStats.totalQuizzes > 0 ? 
                Math.round((this.quizStats.perfectScores / this.quizStats.totalQuizzes) * 100) : 0
        };
    }
}

// Initialize gamification system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.gamificationSystem = new GamificationSystem();
    
    // Add CSS animations for notifications
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .achievement-notification-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .achievement-notification-icon {
            font-size: 2rem;
            opacity: 0.9;
        }
        
        .achievement-notification-title {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }
        
        .achievement-notification-name {
            font-size: 1rem;
            margin-bottom: 3px;
        }
        
        .achievement-notification-points {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    `;
    document.head.appendChild(style);
});

// Helper function to trigger lesson completion
function completeLessonWithGamification(moduleKey) {
    // Call the original lesson completion
    simulateLessonCompletion(moduleKey);
    
    // Trigger gamification events
    document.dispatchEvent(new CustomEvent('lessonCompleted', {
        detail: { module: moduleKey }
    }));
}

// Helper function to complete quiz with gamification
function completeQuizWithGamification(score, maxScore) {
    // Trigger gamification events
    document.dispatchEvent(new CustomEvent('quizCompleted', {
        detail: { score, maxScore }
    }));
    
    return window.gamificationSystem.recordQuizCompletion(score, maxScore);
}
