/* Gamification Styles */

/* Modules Section */
.modules {
    padding: var(--spacing-20) 0;
    background: var(--gray-50);
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-6);
    margin-top: var(--spacing-8);
}

.module-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
    transition: var(--transition-normal);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.module-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-4);
    box-shadow: var(--shadow-md);
}

.module-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: var(--gray-800);
}

.module-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.module-progress {
    margin-bottom: var(--spacing-4);
}

.module-progress .progress-bar {
    height: 6px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-2);
}

.module-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
}

.module-progress .progress-text {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 500;
}

.module-btn {
    width: 100%;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    font-family: inherit;
}

.module-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.module-btn:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Achievements Section */
.achievements {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
    margin-top: var(--spacing-8);
}

.achievement-card {
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    text-align: center;
    transition: var(--transition-normal);
    border: 2px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.achievement-card.unlocked {
    background: linear-gradient(135deg, var(--accent-color), #d97706);
    color: var(--white);
    border-color: var(--accent-color);
    box-shadow: var(--shadow-lg);
}

.achievement-card.unlocked::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

.achievement-icon {
    width: 80px;
    height: 80px;
    background: var(--gray-300);
    color: var(--gray-500);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-3xl);
    margin: 0 auto var(--spacing-4);
    transition: var(--transition-normal);
}

.achievement-card.unlocked .achievement-icon {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.achievement-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
}

.achievement-description {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.achievement-card:not(.unlocked) .achievement-title,
.achievement-card:not(.unlocked) .achievement-description {
    color: var(--gray-500);
}

/* Level Up Animation */
.level-up-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, var(--accent-color), #d97706);
    color: var(--white);
    padding: var(--spacing-6) var(--spacing-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    z-index: 10000;
    text-align: center;
    animation: levelUpAppear 0.5s ease-out;
}

@keyframes levelUpAppear {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.level-up-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-2);
}

.level-up-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
}

/* Points Animation */
.points-animation {
    position: fixed;
    pointer-events: none;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--accent-color);
    z-index: 1000;
    animation: pointsFloat 2s ease-out forwards;
}

@keyframes pointsFloat {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px);
    }
}

/* Quiz Styles */
.quiz-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-lg);
    margin: var(--spacing-8) 0;
}

.quiz-question {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-6);
    color: var(--gray-800);
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-6);
}

.quiz-option {
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-base);
}

.quiz-option:hover {
    background: var(--gray-100);
    border-color: var(--primary-color);
}

.quiz-option.selected {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-dark);
}

.quiz-option.correct {
    background: var(--success-color);
    color: var(--white);
    border-color: var(--success-color);
}

.quiz-option.incorrect {
    background: var(--danger-color);
    color: var(--white);
    border-color: var(--danger-color);
}

.quiz-submit {
    background: var(--secondary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    font-family: inherit;
}

.quiz-submit:hover {
    background: #059669;
}

.quiz-submit:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
}

/* Progress Indicators */
.lesson-progress {
    display: flex;
    justify-content: center;
    gap: var(--spacing-2);
    margin: var(--spacing-6) 0;
}

.progress-dot {
    width: 12px;
    height: 12px;
    border-radius: var(--radius-full);
    background: var(--gray-300);
    transition: var(--transition-fast);
}

.progress-dot.completed {
    background: var(--success-color);
}

.progress-dot.current {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Responsive Gamification */
@media (max-width: 768px) {
    .modules-grid {
        grid-template-columns: 1fr;
    }
    
    .achievements-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .level-up-notification {
        margin: 0 var(--spacing-4);
        width: calc(100% - var(--spacing-8));
    }
    
    .quiz-container {
        margin: var(--spacing-4) 0;
        padding: var(--spacing-6);
    }
}
