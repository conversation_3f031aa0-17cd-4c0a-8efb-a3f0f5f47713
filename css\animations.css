/* Enhanced Animations and Visual Effects */

/* Keyframe Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -15px, 0);
    }
    70% {
        transform: translate3d(0, -7px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--target-width);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-rotate {
    animation: rotate 2s linear infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

/* Delayed Animations */
.animate-delay-100 {
    animation-delay: 0.1s;
}

.animate-delay-200 {
    animation-delay: 0.2s;
}

.animate-delay-300 {
    animation-delay: 0.3s;
}

.animate-delay-400 {
    animation-delay: 0.4s;
}

.animate-delay-500 {
    animation-delay: 0.5s;
}

/* Hover Animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Loading Animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: rotate 1s linear infinite;
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

/* Progress Animations */
.progress-bar-animated .progress-fill {
    animation: progressFill 2s ease-out forwards;
}

.counter-animated {
    animation: countUp 1s ease-out forwards;
}

/* Achievement Animations */
.achievement-unlock {
    animation: scaleIn 0.5s ease-out, glow 2s ease-in-out;
}

.achievement-sparkle {
    position: relative;
    overflow: hidden;
}

.achievement-sparkle::before {
    content: '✨';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.5rem;
    animation: sparkle 2s ease-in-out infinite;
}

.achievement-sparkle::after {
    content: '⭐';
    position: absolute;
    bottom: -10px;
    left: -10px;
    font-size: 1.2rem;
    animation: sparkle 2s ease-in-out infinite 0.5s;
}

/* Notification Animations */
.notification-slide-in {
    animation: slideInFromRight 0.5s ease-out forwards;
}

.notification-slide-out {
    animation: slideInFromRight 0.5s ease-out reverse forwards;
}

/* Typewriter Effect */
.typewriter {
    overflow: hidden;
    border-left: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
}

/* Particle Effects */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.7;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
}

.particle:nth-child(3) {
    left: 30%;
    animation-delay: 2s;
}

.particle:nth-child(4) {
    left: 40%;
    animation-delay: 3s;
}

.particle:nth-child(5) {
    left: 50%;
    animation-delay: 4s;
}

/* Interactive Elements */
.interactive-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.interactive-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.interactive-card:active {
    transform: translateY(-2px) scale(0.98);
}

/* Button Animations */
.btn-animated {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-animated:hover::before {
    left: 100%;
}

.btn-animated:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Icon Animations */
.icon-bounce {
    animation: bounce 2s ease-in-out infinite;
}

.icon-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.icon-rotate {
    animation: rotate 3s linear infinite;
}

/* Text Animations */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Scroll Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .animate-fade-in-up,
    .animate-fade-in-down,
    .animate-fade-in-left,
    .animate-fade-in-right {
        animation-duration: 0.4s;
    }
    
    .hover-lift:hover {
        transform: translateY(-3px);
    }
    
    .interactive-card:hover {
        transform: translateY(-5px) scale(1.01);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .animate-pulse,
    .animate-float,
    .animate-glow,
    .animate-rotate {
        animation: none;
    }
}
