/* Module-specific styles */

/* Module Hero Section */
.module-hero {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: var(--spacing-20) 0 var(--spacing-16);
    margin-top: 80px;
    text-align: center;
}

.module-hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.module-icon-large {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    margin: 0 auto var(--spacing-6);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.module-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
}

.module-description {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-8);
    opacity: 0.9;
}

.module-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-8);
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

/* Learning Path Module */
.learning-path-module {
    padding: var(--spacing-16) 0;
    background: var(--white);
}

.path-steps {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-4);
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
}

.path-steps::before {
    content: '';
    position: absolute;
    top: 30px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gray-200);
    z-index: 1;
}

.step-item {
    flex: 1;
    text-align: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--gray-300);
    color: var(--gray-600);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0 auto var(--spacing-4);
    border: 4px solid var(--white);
    box-shadow: var(--shadow);
}

.step-item.completed .step-number {
    background: var(--success-color);
    color: var(--white);
}

.step-item.current .step-number {
    background: var(--primary-color);
    color: var(--white);
    animation: pulse 2s infinite;
}

.step-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--gray-800);
}

.step-content p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

/* Lessons Section */
.lessons-section {
    padding: var(--spacing-16) 0;
    background: var(--gray-50);
}

.lessons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-6);
    margin-top: var(--spacing-8);
}

.lesson-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
    transition: var(--transition-normal);
    cursor: pointer;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    position: relative;
    overflow: hidden;
}

.lesson-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gray-300);
}

.lesson-card.completed::before {
    background: var(--success-color);
}

.lesson-card.current::before {
    background: var(--primary-color);
}

.lesson-card:hover:not(.locked) {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.lesson-card.locked {
    opacity: 0.6;
    cursor: not-allowed;
}

.lesson-status {
    font-size: var(--font-size-2xl);
    min-width: 40px;
}

.lesson-status .fa-check-circle {
    color: var(--success-color);
}

.lesson-status .fa-play-circle {
    color: var(--primary-color);
}

.lesson-status .fa-lock {
    color: var(--gray-400);
}

.lesson-content {
    flex: 1;
}

.lesson-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--gray-800);
}

.lesson-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-3);
    line-height: 1.6;
}

.lesson-meta {
    display: flex;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.lesson-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.lesson-points {
    background: var(--accent-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    white-space: nowrap;
}

/* Practice Section */
.practice-section {
    padding: var(--spacing-16) 0;
    background: var(--white);
}

.practice-scenarios {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: var(--spacing-8);
    margin-top: var(--spacing-8);
}

.scenario-card {
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    border: 2px solid var(--gray-200);
    transition: var(--transition-normal);
}

.scenario-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.scenario-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.scenario-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
}

.difficulty {
    background: var(--secondary-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.scenario-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.scenario-tasks {
    margin-bottom: var(--spacing-6);
}

.task-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) 0;
    border-bottom: 1px solid var(--gray-200);
}

.task-item:last-child {
    border-bottom: none;
}

.task-item i {
    color: var(--primary-color);
    width: 20px;
}

/* Quiz Section */
.quiz-section {
    padding: var(--spacing-16) 0;
    background: var(--gray-50);
}

.quiz-intro {
    max-width: 600px;
    margin: 0 auto;
}

.quiz-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-lg);
    text-align: center;
}

.quiz-header h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    color: var(--gray-800);
}

.quiz-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    flex-wrap: wrap;
}

.quiz-stats span {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.quiz-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-6);
}

.quiz-requirements {
    background: var(--gray-50);
    padding: var(--spacing-4);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-6);
    text-align: right;
}

.quiz-requirements h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: var(--gray-800);
}

.quiz-requirements ul {
    list-style: none;
    padding: 0;
}

.quiz-requirements li {
    padding: var(--spacing-2) 0;
    color: var(--gray-600);
    position: relative;
    padding-right: var(--spacing-6);
}

.quiz-requirements li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
}

.quiz-start-btn {
    font-size: var(--font-size-lg);
    padding: var(--spacing-4) var(--spacing-8);
}

/* Progress Tracking */
.progress-tracking {
    padding: var(--spacing-16) 0;
    background: var(--white);
}

.progress-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-6);
    margin-top: var(--spacing-8);
}

.progress-card {
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    border: 2px solid var(--gray-200);
    transition: var(--transition-normal);
}

.progress-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.progress-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
}

.progress-info {
    flex: 1;
}

.progress-info h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: var(--gray-800);
}

.progress-info .progress-bar {
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-2);
}

.progress-info .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
}

.progress-text {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.points-display {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: var(--spacing-1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .module-title {
        font-size: var(--font-size-3xl);
    }
    
    .module-stats {
        gap: var(--spacing-4);
    }
    
    .path-steps {
        flex-direction: column;
        gap: var(--spacing-6);
    }
    
    .path-steps::before {
        display: none;
    }
    
    .lessons-grid {
        grid-template-columns: 1fr;
    }
    
    .lesson-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-3);
    }
    
    .practice-scenarios {
        grid-template-columns: 1fr;
    }
    
    .scenario-header {
        flex-direction: column;
        gap: var(--spacing-2);
        align-items: flex-start;
    }
    
    .quiz-stats {
        flex-direction: column;
        gap: var(--spacing-3);
    }
    
    .progress-cards {
        grid-template-columns: 1fr;
    }
    
    .progress-card {
        flex-direction: column;
        text-align: center;
    }
}

/* Animations */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}
