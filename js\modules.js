// Module Management System
class ModuleManager {
    constructor() {
        this.currentModule = null;
        this.currentLesson = 0;
        this.moduleData = this.initializeModuleData();
        this.bindEvents();
    }

    // Initialize module data with lessons and content
    initializeModuleData() {
        return {
            seo: {
                title: 'تحسين محركات البحث (SEO)',
                description: 'تعلم كيفية تحسين موقعك ليظهر في أعلى نتائج البحث',
                icon: 'fas fa-search',
                color: '#3b82f6',
                lessons: [
                    {
                        title: 'مقدمة في تحسين محركات البحث',
                        content: 'تعرف على أساسيات SEO وأهميته في التسويق الرقمي',
                        duration: '15 دقيقة',
                        type: 'video'
                    },
                    {
                        title: 'البحث عن الكلمات المفتاحية',
                        content: 'كيفية العثور على الكلمات المفتاحية المناسبة لموقعك',
                        duration: '20 دقيقة',
                        type: 'interactive'
                    },
                    {
                        title: 'تحسين المحتوى للكلمات المفتاحية',
                        content: 'استراتيجيات كتابة محتوى محسن لمحركات البحث',
                        duration: '25 دقيقة',
                        type: 'text'
                    },
                    {
                        title: 'اختبار: أساسيات SEO',
                        content: 'اختبر معرفتك بأساسيات تحسين محركات البحث',
                        duration: '10 دقائق',
                        type: 'quiz'
                    }
                ]
            },
            social: {
                title: 'التسويق عبر وسائل التواصل الاجتماعي',
                description: 'استراتيجيات فعالة للتسويق على منصات التواصل الاجتماعي',
                icon: 'fab fa-facebook',
                color: '#10b981',
                lessons: [
                    {
                        title: 'استراتيجية وسائل التواصل الاجتماعي',
                        content: 'بناء استراتيجية شاملة للتسويق عبر وسائل التواصل',
                        duration: '18 دقيقة',
                        type: 'video'
                    },
                    {
                        title: 'إنشاء محتوى جذاب',
                        content: 'تعلم كيفية إنشاء محتوى يجذب الجمهور ويحفز التفاعل',
                        duration: '22 دقيقة',
                        type: 'interactive'
                    },
                    {
                        title: 'إدارة المجتمعات الرقمية',
                        content: 'كيفية بناء وإدارة مجتمع نشط حول علامتك التجارية',
                        duration: '20 دقيقة',
                        type: 'text'
                    }
                ]
            },
            email: {
                title: 'التسويق عبر البريد الإلكتروني',
                description: 'بناء قوائم بريدية فعالة وحملات تسويقية ناجحة',
                icon: 'fas fa-envelope',
                color: '#f59e0b',
                lessons: [
                    {
                        title: 'أساسيات التسويق عبر البريد الإلكتروني',
                        content: 'مقدمة شاملة للتسويق عبر البريد الإلكتروني',
                        duration: '16 دقيقة',
                        type: 'video'
                    },
                    {
                        title: 'بناء قائمة بريدية',
                        content: 'استراتيجيات جمع عناوين البريد الإلكتروني بطريقة قانونية',
                        duration: '24 دقيقة',
                        type: 'interactive'
                    }
                ]
            },
            content: {
                title: 'تسويق المحتوى',
                description: 'إنشاء محتوى جذاب يحول الزوار إلى عملاء',
                icon: 'fas fa-pen-fancy',
                color: '#8b5cf6',
                lessons: [
                    {
                        title: 'استراتيجية المحتوى',
                        content: 'تطوير استراتيجية محتوى فعالة لعلامتك التجارية',
                        duration: '20 دقيقة',
                        type: 'video'
                    },
                    {
                        title: 'أنواع المحتوى الرقمي',
                        content: 'تعرف على أنواع المحتوى المختلفة وكيفية استخدامها',
                        duration: '18 دقيقة',
                        type: 'interactive'
                    }
                ]
            },
            ppc: {
                title: 'الإعلانات المدفوعة (PPC)',
                description: 'إدارة حملات إعلانية مربحة على جوجل وفيسبوك',
                icon: 'fas fa-mouse-pointer',
                color: '#ef4444',
                lessons: [
                    {
                        title: 'مقدمة في الإعلانات المدفوعة',
                        content: 'أساسيات الإعلان المدفوع وأنواعه المختلفة',
                        duration: '22 دقيقة',
                        type: 'video'
                    },
                    {
                        title: 'إعلانات جوجل',
                        content: 'كيفية إنشاء وإدارة حملات إعلانية على جوجل',
                        duration: '30 دقيقة',
                        type: 'interactive'
                    }
                ]
            },
            analytics: {
                title: 'تحليل البيانات والقياس',
                description: 'قياس وتحليل أداء حملاتك التسويقية',
                icon: 'fas fa-chart-pie',
                color: '#06b6d4',
                lessons: [
                    {
                        title: 'مقدمة في تحليل البيانات',
                        content: 'أهمية البيانات في اتخاذ القرارات التسويقية',
                        duration: '18 دقيقة',
                        type: 'video'
                    },
                    {
                        title: 'جوجل أناليتكس',
                        content: 'كيفية استخدام جوجل أناليتكس لتتبع أداء موقعك',
                        duration: '25 دقيقة',
                        type: 'interactive'
                    }
                ]
            }
        };
    }

    // Open module and show lessons
    openModule(moduleKey) {
        const progress = window.userProgress;
        const moduleData = progress.modules[moduleKey];
        
        if (!moduleData.unlocked) {
            this.showMessage('هذه الوحدة مقفلة. أكمل الوحدات السابقة لفتحها.', 'warning');
            return;
        }

        this.currentModule = moduleKey;
        this.showModulePage(moduleKey);
    }

    // Show module page with lessons
    showModulePage(moduleKey) {
        const moduleInfo = this.moduleData[moduleKey];
        const progress = window.userProgress.modules[moduleKey];
        
        // Create module page HTML
        const modulePageHTML = `
            <div class="module-page">
                <div class="module-header">
                    <button class="back-btn" onclick="moduleManager.closePage()">
                        <i class="fas fa-arrow-right"></i>
                        العودة للرئيسية
                    </button>
                    <div class="module-info">
                        <div class="module-icon" style="background: ${moduleInfo.color}">
                            <i class="${moduleInfo.icon}"></i>
                        </div>
                        <div class="module-details">
                            <h1 class="module-title">${moduleInfo.title}</h1>
                            <p class="module-description">${moduleInfo.description}</p>
                            <div class="module-progress">
                                <span>التقدم: ${progress.completed}/${progress.total} دروس</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${(progress.completed / progress.total) * 100}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="lessons-container">
                    <h2>الدروس</h2>
                    <div class="lessons-list">
                        ${this.generateLessonsHTML(moduleKey)}
                    </div>
                </div>
            </div>
        `;

        // Show module page
        document.body.insertAdjacentHTML('beforeend', `
            <div class="module-overlay">
                <div class="module-content">
                    ${modulePageHTML}
                </div>
            </div>
        `);

        // Add styles
        this.addModulePageStyles();
    }

    // Generate lessons HTML
    generateLessonsHTML(moduleKey) {
        const moduleInfo = this.moduleData[moduleKey];
        const progress = window.userProgress.modules[moduleKey];
        
        return moduleInfo.lessons.map((lesson, index) => {
            const isCompleted = index < progress.completed;
            const isAvailable = index <= progress.completed;
            const lessonNumber = index + 1;
            
            let statusClass = '';
            let statusIcon = '';
            
            if (isCompleted) {
                statusClass = 'completed';
                statusIcon = '<i class="fas fa-check-circle"></i>';
            } else if (isAvailable) {
                statusClass = 'available';
                statusIcon = '<i class="fas fa-play-circle"></i>';
            } else {
                statusClass = 'locked';
                statusIcon = '<i class="fas fa-lock"></i>';
            }

            return `
                <div class="lesson-item ${statusClass}" ${isAvailable ? `onclick="moduleManager.startLesson('${moduleKey}', ${index})"` : ''}>
                    <div class="lesson-number">${lessonNumber}</div>
                    <div class="lesson-content">
                        <h3 class="lesson-title">${lesson.title}</h3>
                        <p class="lesson-description">${lesson.content}</p>
                        <div class="lesson-meta">
                            <span class="lesson-duration">
                                <i class="fas fa-clock"></i>
                                ${lesson.duration}
                            </span>
                            <span class="lesson-type">
                                <i class="fas fa-${this.getTypeIcon(lesson.type)}"></i>
                                ${this.getTypeLabel(lesson.type)}
                            </span>
                        </div>
                    </div>
                    <div class="lesson-status">
                        ${statusIcon}
                    </div>
                </div>
            `;
        }).join('');
    }

    // Get type icon
    getTypeIcon(type) {
        const icons = {
            video: 'video',
            interactive: 'gamepad',
            text: 'book',
            quiz: 'question-circle'
        };
        return icons[type] || 'file';
    }

    // Get type label
    getTypeLabel(type) {
        const labels = {
            video: 'فيديو',
            interactive: 'تفاعلي',
            text: 'نص',
            quiz: 'اختبار'
        };
        return labels[type] || 'درس';
    }

    // Start lesson
    startLesson(moduleKey, lessonIndex) {
        const moduleInfo = this.moduleData[moduleKey];
        const lesson = moduleInfo.lessons[lessonIndex];
        
        if (lesson.type === 'quiz') {
            this.startQuiz(moduleKey, lessonIndex);
        } else {
            this.showLesson(moduleKey, lessonIndex);
        }
    }

    // Show lesson content
    showLesson(moduleKey, lessonIndex) {
        const moduleInfo = this.moduleData[moduleKey];
        const lesson = moduleInfo.lessons[lessonIndex];
        
        // Simulate lesson content
        const lessonHTML = `
            <div class="lesson-viewer">
                <div class="lesson-header">
                    <button class="back-btn" onclick="moduleManager.closePage()">
                        <i class="fas fa-arrow-right"></i>
                        العودة للوحدة
                    </button>
                    <h1>${lesson.title}</h1>
                </div>
                
                <div class="lesson-content">
                    <div class="lesson-body">
                        <p>${lesson.content}</p>
                        <div class="lesson-placeholder">
                            <i class="fas fa-${this.getTypeIcon(lesson.type)} fa-3x"></i>
                            <p>محتوى الدرس سيكون متاحاً هنا</p>
                            <p>نوع الدرس: ${this.getTypeLabel(lesson.type)}</p>
                            <p>المدة: ${lesson.duration}</p>
                        </div>
                    </div>
                    
                    <div class="lesson-actions">
                        <button class="btn btn-primary" onclick="moduleManager.completeLesson('${moduleKey}', ${lessonIndex})">
                            <i class="fas fa-check"></i>
                            إكمال الدرس
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Replace current content
        const moduleContent = document.querySelector('.module-content');
        if (moduleContent) {
            moduleContent.innerHTML = lessonHTML;
        }
    }

    // Start quiz
    startQuiz(moduleKey, lessonIndex) {
        // Simple quiz example
        const quizQuestions = [
            {
                question: 'ما هو الهدف الأساسي من تحسين محركات البحث؟',
                options: [
                    'زيادة سرعة الموقع',
                    'تحسين ترتيب الموقع في نتائج البحث',
                    'تقليل تكلفة الاستضافة',
                    'زيادة حجم الموقع'
                ],
                correct: 1
            },
            {
                question: 'أي من هذه العوامل مهم في SEO؟',
                options: [
                    'الكلمات المفتاحية',
                    'جودة المحتوى',
                    'سرعة التحميل',
                    'جميع ما سبق'
                ],
                correct: 3
            }
        ];

        this.showQuiz(moduleKey, lessonIndex, quizQuestions);
    }

    // Show quiz interface
    showQuiz(moduleKey, lessonIndex, questions) {
        let currentQuestion = 0;
        let score = 0;
        let answers = [];

        const showQuestion = () => {
            const question = questions[currentQuestion];
            const quizHTML = `
                <div class="quiz-viewer">
                    <div class="quiz-header">
                        <button class="back-btn" onclick="moduleManager.closePage()">
                            <i class="fas fa-arrow-right"></i>
                            العودة للوحدة
                        </button>
                        <h1>اختبار الوحدة</h1>
                        <div class="quiz-progress">
                            السؤال ${currentQuestion + 1} من ${questions.length}
                        </div>
                    </div>
                    
                    <div class="quiz-content">
                        <div class="quiz-question">
                            <h2>${question.question}</h2>
                        </div>
                        
                        <div class="quiz-options">
                            ${question.options.map((option, index) => `
                                <button class="quiz-option" onclick="selectAnswer(${index})">
                                    ${option}
                                </button>
                            `).join('')}
                        </div>
                        
                        <div class="quiz-actions">
                            <button class="btn btn-primary" id="nextBtn" onclick="nextQuestion()" disabled>
                                ${currentQuestion === questions.length - 1 ? 'إنهاء الاختبار' : 'السؤال التالي'}
                            </button>
                        </div>
                    </div>
                </div>
            `;

            const moduleContent = document.querySelector('.module-content');
            if (moduleContent) {
                moduleContent.innerHTML = quizHTML;
            }

            // Add quiz functions to window
            window.selectAnswer = (selectedIndex) => {
                // Remove previous selections
                document.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // Mark selected option
                document.querySelectorAll('.quiz-option')[selectedIndex].classList.add('selected');
                
                // Store answer
                answers[currentQuestion] = selectedIndex;
                
                // Enable next button
                document.getElementById('nextBtn').disabled = false;
            };

            window.nextQuestion = () => {
                // Check if answer is correct
                if (answers[currentQuestion] === question.correct) {
                    score++;
                }

                currentQuestion++;
                
                if (currentQuestion < questions.length) {
                    showQuestion();
                } else {
                    this.showQuizResults(moduleKey, lessonIndex, score, questions.length);
                }
            };
        };

        showQuestion();
    }

    // Show quiz results
    showQuizResults(moduleKey, lessonIndex, score, total) {
        const percentage = Math.round((score / total) * 100);
        const passed = percentage >= 70;

        const resultsHTML = `
            <div class="quiz-results">
                <div class="results-header">
                    <h1>نتائج الاختبار</h1>
                </div>
                
                <div class="results-content">
                    <div class="score-display ${passed ? 'passed' : 'failed'}">
                        <div class="score-circle">
                            <span class="score-percentage">${percentage}%</span>
                        </div>
                        <h2>${passed ? 'تهانينا! لقد نجحت' : 'لم تنجح هذه المرة'}</h2>
                        <p>حصلت على ${score} من ${total} إجابات صحيحة</p>
                    </div>
                    
                    <div class="results-actions">
                        ${passed ? `
                            <button class="btn btn-primary" onclick="moduleManager.completeLesson('${moduleKey}', ${lessonIndex})">
                                <i class="fas fa-check"></i>
                                متابعة
                            </button>
                        ` : `
                            <button class="btn btn-secondary" onclick="moduleManager.startQuiz('${moduleKey}', ${lessonIndex})">
                                <i class="fas fa-redo"></i>
                                إعادة المحاولة
                            </button>
                        `}
                        <button class="btn btn-outline" onclick="moduleManager.openModule('${moduleKey}')">
                            العودة للوحدة
                        </button>
                    </div>
                </div>
            </div>
        `;

        const moduleContent = document.querySelector('.module-content');
        if (moduleContent) {
            moduleContent.innerHTML = resultsHTML;
        }

        // Record quiz completion in gamification system
        if (window.gamificationSystem) {
            completeQuizWithGamification(score, total);
        }
    }

    // Complete lesson
    completeLesson(moduleKey, lessonIndex) {
        const progress = window.userProgress.modules[moduleKey];
        
        // Mark lesson as completed
        if (lessonIndex === progress.completed) {
            progress.completed++;
            
            // Add points
            const pointsEarned = 50;
            window.userProgress.points += pointsEarned;
            
            // Check if module is completed
            if (progress.completed === progress.total) {
                this.unlockNextModule(moduleKey);
                this.showMessage(`تهانينا! لقد أكملت وحدة ${this.moduleData[moduleKey].title}`, 'success');
            }
            
            // Update total progress
            updateTotalProgress();
            
            // Save progress
            saveUserProgress();
            
            // Update UI
            updateProgressDisplay();
            
            // Trigger gamification
            if (window.gamificationSystem) {
                completeLessonWithGamification(moduleKey);
            }
            
            // Show success message
            this.showMessage(`تم إكمال الدرس بنجاح! حصلت على ${pointsEarned} نقطة`, 'success');
        }
        
        // Return to module page
        this.openModule(moduleKey);
    }

    // Unlock next module
    unlockNextModule(completedModule) {
        const moduleOrder = ['seo', 'social', 'email', 'content', 'ppc', 'analytics'];
        const currentIndex = moduleOrder.indexOf(completedModule);
        
        if (currentIndex >= 0 && currentIndex < moduleOrder.length - 1) {
            const nextModule = moduleOrder[currentIndex + 1];
            window.userProgress.modules[nextModule].unlocked = true;
            this.showMessage(`تم فتح وحدة جديدة: ${this.moduleData[nextModule].title}`, 'info');
        }
    }

    // Close module page
    closePage() {
        const overlay = document.querySelector('.module-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    // Show message
    showMessage(message, type = 'info') {
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--${type === 'success' ? 'success' : type === 'warning' ? 'accent' : 'primary'}-color);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10001;
            max-width: 300px;
            box-shadow: var(--shadow-lg);
        `;
        
        document.body.appendChild(messageEl);
        
        setTimeout(() => {
            if (document.body.contains(messageEl)) {
                document.body.removeChild(messageEl);
            }
        }, 3000);
    }

    // Add module page styles
    addModulePageStyles() {
        if (document.getElementById('moduleStyles')) return;
        
        const style = document.createElement('style');
        style.id = 'moduleStyles';
        style.textContent = `
            .module-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }
            
            .module-content {
                background: white;
                border-radius: 15px;
                max-width: 800px;
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
                direction: rtl;
            }
            
            .module-header {
                padding: 30px;
                border-bottom: 1px solid var(--gray-200);
            }
            
            .back-btn {
                background: var(--gray-100);
                border: none;
                padding: 10px 15px;
                border-radius: 8px;
                cursor: pointer;
                margin-bottom: 20px;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .module-info {
                display: flex;
                gap: 20px;
                align-items: center;
            }
            
            .module-icon {
                width: 80px;
                height: 80px;
                border-radius: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 2rem;
            }
            
            .module-title {
                font-size: 1.8rem;
                margin-bottom: 10px;
                color: var(--gray-800);
            }
            
            .module-description {
                color: var(--gray-600);
                margin-bottom: 15px;
            }
            
            .lessons-container {
                padding: 30px;
            }
            
            .lessons-list {
                display: flex;
                flex-direction: column;
                gap: 15px;
                margin-top: 20px;
            }
            
            .lesson-item {
                display: flex;
                align-items: center;
                gap: 20px;
                padding: 20px;
                border: 2px solid var(--gray-200);
                border-radius: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .lesson-item.available:hover {
                border-color: var(--primary-color);
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }
            
            .lesson-item.completed {
                background: var(--gray-50);
                border-color: var(--success-color);
            }
            
            .lesson-item.locked {
                opacity: 0.5;
                cursor: not-allowed;
            }
            
            .lesson-number {
                width: 40px;
                height: 40px;
                background: var(--primary-color);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
            }
            
            .lesson-content {
                flex: 1;
            }
            
            .lesson-title {
                font-size: 1.2rem;
                margin-bottom: 8px;
                color: var(--gray-800);
            }
            
            .lesson-description {
                color: var(--gray-600);
                margin-bottom: 10px;
            }
            
            .lesson-meta {
                display: flex;
                gap: 20px;
                font-size: 0.9rem;
                color: var(--gray-500);
            }
            
            .lesson-status {
                font-size: 1.5rem;
            }
            
            .lesson-status .fa-check-circle {
                color: var(--success-color);
            }
            
            .lesson-status .fa-play-circle {
                color: var(--primary-color);
            }
            
            .lesson-status .fa-lock {
                color: var(--gray-400);
            }
            
            .lesson-viewer, .quiz-viewer, .quiz-results {
                padding: 30px;
            }
            
            .lesson-placeholder {
                text-align: center;
                padding: 60px 20px;
                background: var(--gray-50);
                border-radius: 12px;
                margin: 30px 0;
                color: var(--gray-500);
            }
            
            .lesson-actions {
                text-align: center;
                margin-top: 30px;
            }
            
            .quiz-question h2 {
                font-size: 1.4rem;
                margin-bottom: 30px;
                color: var(--gray-800);
            }
            
            .quiz-options {
                display: flex;
                flex-direction: column;
                gap: 15px;
                margin-bottom: 30px;
            }
            
            .quiz-option {
                background: var(--gray-50);
                border: 2px solid var(--gray-200);
                padding: 15px 20px;
                border-radius: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
                text-align: right;
            }
            
            .quiz-option:hover {
                border-color: var(--primary-color);
                background: var(--gray-100);
            }
            
            .quiz-option.selected {
                background: var(--primary-color);
                color: white;
                border-color: var(--primary-dark);
            }
            
            .score-display {
                text-align: center;
                margin-bottom: 30px;
            }
            
            .score-circle {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 20px;
                font-size: 2rem;
                font-weight: bold;
            }
            
            .score-display.passed .score-circle {
                background: var(--success-color);
                color: white;
            }
            
            .score-display.failed .score-circle {
                background: var(--danger-color);
                color: white;
            }
            
            .results-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .btn-outline {
                background: transparent;
                color: var(--primary-color);
                border: 2px solid var(--primary-color);
            }
            
            .btn-outline:hover {
                background: var(--primary-color);
                color: white;
            }
        `;
        
        document.head.appendChild(style);
    }

    // Bind events
    bindEvents() {
        // Override the global openModule function
        window.openModule = (moduleKey) => {
            this.openModule(moduleKey);
        };
    }
}

// Initialize module manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.moduleManager = new ModuleManager();
});
