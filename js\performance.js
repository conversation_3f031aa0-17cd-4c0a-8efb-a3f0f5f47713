// Performance Optimization and Monitoring
class PerformanceManager {
    constructor() {
        this.metrics = {
            loadTime: 0,
            domContentLoaded: 0,
            firstContentfulPaint: 0,
            largestContentfulPaint: 0,
            cumulativeLayoutShift: 0,
            firstInputDelay: 0
        };
        this.init();
    }

    // Initialize performance monitoring
    init() {
        this.measureLoadTimes();
        this.setupLazyLoading();
        this.optimizeAnimations();
        this.setupErrorHandling();
        this.monitorMemoryUsage();
    }

    // Measure page load times
    measureLoadTimes() {
        // Wait for page to fully load
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            
            this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
            this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
            
            // Measure Core Web Vitals
            this.measureCoreWebVitals();
            
            // Log performance metrics in development
            if (window.location.hostname === 'localhost') {
                console.log('Performance Metrics:', this.metrics);
            }
        });
    }

    // Measure Core Web Vitals
    measureCoreWebVitals() {
        // First Contentful Paint
        const paintEntries = performance.getEntriesByType('paint');
        const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
            this.metrics.firstContentfulPaint = fcpEntry.startTime;
        }

        // Largest Contentful Paint
        if ('PerformanceObserver' in window) {
            const lcpObserver = new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.largestContentfulPaint = lastEntry.startTime;
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

            // Cumulative Layout Shift
            const clsObserver = new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    if (!entry.hadRecentInput) {
                        this.metrics.cumulativeLayoutShift += entry.value;
                    }
                }
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });

            // First Input Delay
            const fidObserver = new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
                }
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
        }
    }

    // Setup lazy loading for images and content
    setupLazyLoading() {
        // Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for browsers without IntersectionObserver
            images.forEach(img => {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            });
        }

        // Lazy load modules
        this.setupModuleLazyLoading();
    }

    // Setup lazy loading for modules
    setupModuleLazyLoading() {
        const moduleCards = document.querySelectorAll('.module-card');
        
        if ('IntersectionObserver' in window) {
            const moduleObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const card = entry.target;
                        this.preloadModuleAssets(card.dataset.module);
                        moduleObserver.unobserve(card);
                    }
                });
            }, { rootMargin: '100px' });

            moduleCards.forEach(card => moduleObserver.observe(card));
        }
    }

    // Preload module assets
    preloadModuleAssets(moduleId) {
        if (!moduleId) return;

        // Preload module CSS
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = `modules/${moduleId}.html`;
        document.head.appendChild(link);
    }

    // Optimize animations for performance
    optimizeAnimations() {
        // Reduce animations on low-end devices
        if (this.isLowEndDevice()) {
            document.body.classList.add('reduce-animations');
            
            // Add CSS for reduced animations
            const style = document.createElement('style');
            style.textContent = `
                .reduce-animations * {
                    animation-duration: 0.1s !important;
                    transition-duration: 0.1s !important;
                }
                .reduce-animations .animate-pulse,
                .reduce-animations .animate-float,
                .reduce-animations .animate-glow {
                    animation: none !important;
                }
            `;
            document.head.appendChild(style);
        }

        // Use requestAnimationFrame for smooth animations
        this.optimizeScrollAnimations();
    }

    // Check if device is low-end
    isLowEndDevice() {
        // Check for various indicators of low-end devices
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        const slowConnection = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');
        const lowMemory = navigator.deviceMemory && navigator.deviceMemory < 4;
        const lowCores = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4;
        
        return slowConnection || lowMemory || lowCores;
    }

    // Optimize scroll animations
    optimizeScrollAnimations() {
        let ticking = false;

        const updateScrollAnimations = () => {
            // Update scroll-based animations here
            ticking = false;
        };

        const onScroll = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollAnimations);
                ticking = true;
            }
        };

        window.addEventListener('scroll', onScroll, { passive: true });
    }

    // Setup error handling
    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (event) => {
            this.logError('JavaScript Error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Unhandled Promise Rejection', {
                reason: event.reason
            });
        });

        // Resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.logError('Resource Loading Error', {
                    element: event.target.tagName,
                    source: event.target.src || event.target.href,
                    message: 'Failed to load resource'
                });
            }
        }, true);
    }

    // Log errors (in production, send to monitoring service)
    logError(type, details) {
        const errorData = {
            type: type,
            details: details,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            metrics: this.metrics
        };

        // In development, log to console
        if (window.location.hostname === 'localhost') {
            console.error('Performance Manager Error:', errorData);
        } else {
            // In production, send to monitoring service
            // this.sendToMonitoringService(errorData);
        }
    }

    // Monitor memory usage
    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const memoryUsage = {
                    used: Math.round(memory.usedJSHeapSize / 1048576), // MB
                    total: Math.round(memory.totalJSHeapSize / 1048576), // MB
                    limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
                };

                // Warn if memory usage is high
                if (memoryUsage.used > memoryUsage.limit * 0.8) {
                    console.warn('High memory usage detected:', memoryUsage);
                    this.optimizeMemoryUsage();
                }
            }, 30000); // Check every 30 seconds
        }
    }

    // Optimize memory usage
    optimizeMemoryUsage() {
        // Clear unused event listeners
        this.clearUnusedEventListeners();
        
        // Clear cached data if necessary
        this.clearCachedData();
        
        // Suggest garbage collection (if available)
        if (window.gc) {
            window.gc();
        }
    }

    // Clear unused event listeners
    clearUnusedEventListeners() {
        // Remove event listeners from elements that are no longer in the DOM
        // This is a simplified example - implement based on your specific needs
    }

    // Clear cached data
    clearCachedData() {
        // Clear old progress data if it's taking up too much space
        try {
            const progressData = localStorage.getItem('userProgress');
            if (progressData && progressData.length > 100000) { // 100KB
                // Keep only essential data
                const progress = JSON.parse(progressData);
                const essentialData = {
                    level: progress.level,
                    points: progress.points,
                    completedLessons: progress.completedLessons,
                    achievements: progress.achievements
                };
                localStorage.setItem('userProgress', JSON.stringify(essentialData));
            }
        } catch (error) {
            console.warn('Error clearing cached data:', error);
        }
    }

    // Preload critical resources
    preloadCriticalResources() {
        const criticalResources = [
            'css/main.css',
            'css/gamification.css',
            'css/animations.css',
            'js/main.js',
            'js/gamification.js'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            link.href = resource;
            document.head.appendChild(link);
        });
    }

    // Get performance report
    getPerformanceReport() {
        return {
            metrics: this.metrics,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            connection: this.getConnectionInfo(),
            memory: this.getMemoryInfo()
        };
    }

    // Get connection information
    getConnectionInfo() {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (connection) {
            return {
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt,
                saveData: connection.saveData
            };
        }
        return null;
    }

    // Get memory information
    getMemoryInfo() {
        if ('memory' in performance) {
            const memory = performance.memory;
            return {
                used: Math.round(memory.usedJSHeapSize / 1048576),
                total: Math.round(memory.totalJSHeapSize / 1048576),
                limit: Math.round(memory.jsHeapSizeLimit / 1048576)
            };
        }
        return null;
    }
}

// Initialize performance manager
document.addEventListener('DOMContentLoaded', function() {
    window.performanceManager = new PerformanceManager();
    
    // Expose performance report function globally
    window.getPerformanceReport = () => {
        return window.performanceManager.getPerformanceReport();
    };
});
