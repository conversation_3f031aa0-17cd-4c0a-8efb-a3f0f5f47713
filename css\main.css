/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --danger-color: #ef4444;
    --success-color: #22c55e;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Typography */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    direction: rtl;
    text-align: right;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

/* Header and Navigation */
.header {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: var(--spacing-4) 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-8);
}

.nav-link {
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    transition: var(--transition-fast);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
}

.nav-link:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--gray-600);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Progress Header */
.progress-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: var(--spacing-3) 0;
    margin-top: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-6);
}

.user-level {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-weight: 600;
}

.level-number {
    background: var(--accent-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
}

.progress-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    flex: 1;
    max-width: 300px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--accent-color);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

.progress-text {
    font-size: var(--font-size-sm);
    font-weight: 600;
    min-width: 40px;
}

.user-points {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-weight: 600;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: var(--spacing-20) 0;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-12);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-6);
}

.highlight {
    color: var(--accent-color);
}

.hero-description {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-8);
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    font-family: inherit;
}

.btn-primary {
    background: var(--accent-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #d97706;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 400px;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-icon {
    position: absolute;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--accent-color);
    animation: float 3s ease-in-out infinite;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-icon:nth-child(1) {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.floating-icon:nth-child(2) {
    top: 60%;
    right: 20%;
    animation-delay: 0.5s;
}

.floating-icon:nth-child(3) {
    top: 10%;
    left: 20%;
    animation-delay: 1s;
}

.floating-icon:nth-child(4) {
    top: 70%;
    left: 10%;
    animation-delay: 1.5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Section Styles */
.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-4);
    color: var(--gray-800);
}

.section-description {
    font-size: var(--font-size-lg);
    text-align: center;
    color: var(--gray-600);
    margin-bottom: var(--spacing-12);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Learning Path */
.learning-path {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.path-timeline {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-8);
    max-width: 800px;
    margin: 0 auto;
}

.timeline-item {
    flex: 1;
    text-align: center;
    position: relative;
}

.timeline-item::after {
    content: '';
    position: absolute;
    top: 40px;
    left: 50%;
    width: 100%;
    height: 2px;
    background: var(--gray-200);
    z-index: 1;
}

.timeline-item:last-child::after {
    display: none;
}

.timeline-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    margin: 0 auto var(--spacing-4);
    position: relative;
    z-index: 2;
    box-shadow: var(--shadow-lg);
}

.timeline-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    color: var(--gray-800);
}

.timeline-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-3);
}

.level-badge {
    background: var(--secondary-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: inline-block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        right: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-4) 0;
    }

    .nav-menu.active {
        right: 0;
    }

    .hamburger {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .progress-header {
        flex-direction: column;
        gap: var(--spacing-3);
        padding: var(--spacing-4) var(--spacing-4);
    }

    .progress-container {
        max-width: 100%;
    }

    .path-timeline {
        flex-direction: column;
        gap: var(--spacing-6);
    }

    .timeline-item::after {
        display: none;
    }

    .hero-buttons {
        justify-content: center;
    }
}
