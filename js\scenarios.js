// Interactive Scenarios System
class ScenarioManager {
    constructor() {
        this.scenarios = this.initializeScenarios();
        this.currentScenario = null;
        this.currentStep = 0;
        this.userChoices = [];
        this.bindEvents();
    }

    // Initialize scenario data
    initializeScenarios() {
        return {
            shoe_store: {
                id: 'shoe_store',
                title: 'متجر إلكتروني للأحذية',
                description: 'لديك متجر إلكتروني يبيع الأحذية الرياضية. كيف ستحسن موقعك ليظهر عندما يبحث الناس عن "أحذية رياضية"؟',
                difficulty: 'مبتدئ',
                category: 'seo',
                estimatedTime: '20 دقيقة',
                points: 150,
                steps: [
                    {
                        title: 'تحليل السوق والمنافسين',
                        description: 'ابدأ بفهم السوق والمنافسين في مجال الأحذية الرياضية',
                        type: 'analysis',
                        content: {
                            scenario: 'أنت تدير متجراً إلكترونياً جديداً للأحذية الرياضية. السوق مليء بالمنافسين الكبار مثل نايكي وأديداس. كيف ستبدأ؟',
                            options: [
                                {
                                    text: 'أبحث عن الكلمات المفتاحية التي يستخدمها المنافسون',
                                    points: 20,
                                    feedback: 'ممتاز! تحليل المنافسين خطوة أساسية في استراتيجية SEO',
                                    correct: true
                                },
                                {
                                    text: 'أنشئ موقعاً مشابهاً للمنافسين تماماً',
                                    points: 5,
                                    feedback: 'ليس الخيار الأفضل. التقليد لن يساعدك في التميز',
                                    correct: false
                                },
                                {
                                    text: 'أركز على الإعلانات المدفوعة فقط',
                                    points: 10,
                                    feedback: 'الإعلانات مهمة، لكن SEO يوفر نتائج طويلة المدى',
                                    correct: false
                                }
                            ]
                        }
                    },
                    {
                        title: 'البحث عن الكلمات المفتاحية',
                        description: 'اختر أفضل الكلمات المفتاحية لمتجر الأحذية',
                        type: 'keyword_research',
                        content: {
                            scenario: 'باستخدام أدوات البحث، وجدت هذه الكلمات المفتاحية. أيها الأفضل للبدء؟',
                            keywords: [
                                {
                                    keyword: 'أحذية رياضية',
                                    volume: 10000,
                                    difficulty: 'عالية',
                                    competition: 'شديدة',
                                    recommended: false
                                },
                                {
                                    keyword: 'أحذية جري للنساء',
                                    volume: 2000,
                                    difficulty: 'متوسطة',
                                    competition: 'متوسطة',
                                    recommended: true
                                },
                                {
                                    keyword: 'حذاء رياضي رخيص',
                                    volume: 1500,
                                    difficulty: 'منخفضة',
                                    competition: 'قليلة',
                                    recommended: true
                                }
                            ],
                            task: 'اختر كلمتين مفتاحيتين للتركيز عليهما في البداية'
                        }
                    },
                    {
                        title: 'تحسين صفحة المنتج',
                        description: 'اكتب عنوان ووصف محسن لصفحة منتج',
                        type: 'content_optimization',
                        content: {
                            scenario: 'لديك صفحة منتج لحذاء جري للنساء. اكتب عنواناً ووصفاً محسناً',
                            product: {
                                name: 'حذاء جري نايكي للنساء',
                                features: ['خفيف الوزن', 'مقاوم للماء', 'مريح', 'متين'],
                                price: '299 ريال',
                                colors: ['أسود', 'أبيض', 'وردي']
                            },
                            task: 'اكتب عنوان الصفحة (Title) ووصف تعريفي (Meta Description)'
                        }
                    }
                ]
            },
            cooking_blog: {
                id: 'cooking_blog',
                title: 'مدونة طبخ',
                description: 'تدير مدونة طبخ وتريد زيادة الزوار من محركات البحث',
                difficulty: 'متوسط',
                category: 'content_marketing',
                estimatedTime: '25 دقيقة',
                points: 200,
                steps: [
                    {
                        title: 'اختيار موضوع المقال',
                        description: 'اختر موضوعاً مناسباً لمقال جديد في مدونة الطبخ',
                        type: 'content_planning',
                        content: {
                            scenario: 'تريد كتابة مقال جديد في مدونة الطبخ. أي موضوع سيجذب أكثر زوار؟',
                            options: [
                                {
                                    text: 'طريقة عمل الكبسة السعودية',
                                    points: 25,
                                    feedback: 'ممتاز! الأطباق المحلية تحظى بشعبية كبيرة',
                                    correct: true
                                },
                                {
                                    text: 'تاريخ الطبخ في العالم',
                                    points: 10,
                                    feedback: 'موضوع مثير لكن قد لا يجذب الباحثين عن وصفات',
                                    correct: false
                                },
                                {
                                    text: 'نصائح عامة للطبخ',
                                    points: 15,
                                    feedback: 'جيد، لكن الوصفات المحددة أكثر جذباً',
                                    correct: false
                                }
                            ]
                        }
                    }
                ]
            },
            social_campaign: {
                id: 'social_campaign',
                title: 'حملة تسويقية على وسائل التواصل',
                description: 'أنشئ حملة تسويقية فعالة لمطعم جديد',
                difficulty: 'متوسط',
                category: 'social_media',
                estimatedTime: '30 دقيقة',
                points: 250,
                steps: [
                    {
                        title: 'تحديد الجمهور المستهدف',
                        description: 'حدد الجمهور المناسب لمطعم الوجبات السريعة الصحية',
                        type: 'audience_targeting',
                        content: {
                            scenario: 'مطعم جديد للوجبات السريعة الصحية في الرياض. من هو جمهورك المستهدف؟',
                            demographics: [
                                {
                                    group: 'الشباب 18-25 سنة',
                                    interests: ['الرياضة', 'الصحة', 'الطعام السريع'],
                                    income: 'متوسط',
                                    recommended: true
                                },
                                {
                                    group: 'العائلات 30-45 سنة',
                                    interests: ['الصحة', 'الأطفال', 'الطبخ'],
                                    income: 'متوسط إلى عالي',
                                    recommended: true
                                },
                                {
                                    group: 'كبار السن 50+ سنة',
                                    interests: ['الصحة', 'التقاليد'],
                                    income: 'متنوع',
                                    recommended: false
                                }
                            ]
                        }
                    }
                ]
            }
        };
    }

    // Start a scenario
    startScenario(scenarioId) {
        const scenario = this.scenarios[scenarioId];
        if (!scenario) {
            console.error('Scenario not found:', scenarioId);
            return;
        }

        this.currentScenario = scenario;
        this.currentStep = 0;
        this.userChoices = [];
        
        this.showScenarioInterface();
    }

    // Show scenario interface
    showScenarioInterface() {
        const scenario = this.currentScenario;
        const step = scenario.steps[this.currentStep];

        const scenarioHTML = `
            <div class="scenario-overlay">
                <div class="scenario-container">
                    <div class="scenario-header">
                        <button class="close-scenario" onclick="scenarioManager.closeScenario()">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="scenario-info">
                            <h2>${scenario.title}</h2>
                            <div class="scenario-meta">
                                <span class="difficulty">${scenario.difficulty}</span>
                                <span class="time"><i class="fas fa-clock"></i> ${scenario.estimatedTime}</span>
                                <span class="points"><i class="fas fa-star"></i> ${scenario.points} نقطة</span>
                            </div>
                        </div>
                        <div class="scenario-progress">
                            <span>الخطوة ${this.currentStep + 1} من ${scenario.steps.length}</span>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${((this.currentStep + 1) / scenario.steps.length) * 100}%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="scenario-content">
                        <div class="step-header">
                            <h3>${step.title}</h3>
                            <p>${step.description}</p>
                        </div>
                        
                        <div class="step-content">
                            ${this.renderStepContent(step)}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', scenarioHTML);
        this.addScenarioStyles();
    }

    // Render step content based on type
    renderStepContent(step) {
        switch (step.type) {
            case 'analysis':
                return this.renderAnalysisStep(step.content);
            case 'keyword_research':
                return this.renderKeywordResearchStep(step.content);
            case 'content_optimization':
                return this.renderContentOptimizationStep(step.content);
            case 'content_planning':
                return this.renderContentPlanningStep(step.content);
            case 'audience_targeting':
                return this.renderAudienceTargetingStep(step.content);
            default:
                return '<p>نوع الخطوة غير مدعوم</p>';
        }
    }

    // Render analysis step
    renderAnalysisStep(content) {
        return `
            <div class="scenario-question">
                <p>${content.scenario}</p>
                <div class="options-container">
                    ${content.options.map((option, index) => `
                        <button class="option-btn" onclick="scenarioManager.selectOption(${index})">
                            ${option.text}
                        </button>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Render keyword research step
    renderKeywordResearchStep(content) {
        return `
            <div class="keyword-research">
                <p>${content.scenario}</p>
                <div class="keywords-table">
                    <div class="table-header">
                        <span>الكلمة المفتاحية</span>
                        <span>حجم البحث</span>
                        <span>الصعوبة</span>
                        <span>المنافسة</span>
                        <span>اختيار</span>
                    </div>
                    ${content.keywords.map((keyword, index) => `
                        <div class="keyword-row">
                            <span class="keyword-text">${keyword.keyword}</span>
                            <span class="volume">${keyword.volume.toLocaleString('ar-SA')}</span>
                            <span class="difficulty ${keyword.difficulty}">${keyword.difficulty}</span>
                            <span class="competition ${keyword.competition}">${keyword.competition}</span>
                            <label class="checkbox-container">
                                <input type="checkbox" onchange="scenarioManager.toggleKeyword(${index})">
                                <span class="checkmark"></span>
                            </label>
                        </div>
                    `).join('')}
                </div>
                <p class="task-instruction">${content.task}</p>
                <button class="btn btn-primary" onclick="scenarioManager.submitKeywordSelection()" disabled id="submitKeywords">
                    تأكيد الاختيار
                </button>
            </div>
        `;
    }

    // Render content optimization step
    renderContentOptimizationStep(content) {
        return `
            <div class="content-optimization">
                <p>${content.scenario}</p>
                <div class="product-info">
                    <h4>معلومات المنتج:</h4>
                    <ul>
                        <li><strong>الاسم:</strong> ${content.product.name}</li>
                        <li><strong>المميزات:</strong> ${content.product.features.join(', ')}</li>
                        <li><strong>السعر:</strong> ${content.product.price}</li>
                        <li><strong>الألوان:</strong> ${content.product.colors.join(', ')}</li>
                    </ul>
                </div>
                <div class="optimization-form">
                    <div class="form-group">
                        <label for="pageTitle">عنوان الصفحة (Title):</label>
                        <input type="text" id="pageTitle" placeholder="اكتب عنواناً جذاباً ومحسناً..." maxlength="60">
                        <small>الحد الأقصى: 60 حرف</small>
                    </div>
                    <div class="form-group">
                        <label for="metaDescription">الوصف التعريفي (Meta Description):</label>
                        <textarea id="metaDescription" placeholder="اكتب وصفاً مقنعاً يشجع على النقر..." maxlength="160"></textarea>
                        <small>الحد الأقصى: 160 حرف</small>
                    </div>
                    <button class="btn btn-primary" onclick="scenarioManager.submitContentOptimization()">
                        تأكيد المحتوى
                    </button>
                </div>
            </div>
        `;
    }

    // Render content planning step
    renderContentPlanningStep(content) {
        return this.renderAnalysisStep(content);
    }

    // Render audience targeting step
    renderAudienceTargetingStep(content) {
        return `
            <div class="audience-targeting">
                <p>${content.scenario}</p>
                <div class="demographics-grid">
                    ${content.demographics.map((demo, index) => `
                        <div class="demographic-card ${demo.recommended ? 'recommended' : ''}">
                            <h4>${demo.group}</h4>
                            <div class="demo-details">
                                <p><strong>الاهتمامات:</strong> ${demo.interests.join(', ')}</p>
                                <p><strong>الدخل:</strong> ${demo.income}</p>
                            </div>
                            <label class="checkbox-container">
                                <input type="checkbox" onchange="scenarioManager.toggleDemographic(${index})" ${demo.recommended ? 'checked' : ''}>
                                <span class="checkmark"></span>
                                <span class="label-text">استهداف هذه الفئة</span>
                            </label>
                        </div>
                    `).join('')}
                </div>
                <button class="btn btn-primary" onclick="scenarioManager.submitAudienceSelection()">
                    تأكيد الاختيار
                </button>
            </div>
        `;
    }

    // Handle option selection
    selectOption(optionIndex) {
        const step = this.currentScenario.steps[this.currentStep];
        const option = step.content.options[optionIndex];
        
        this.userChoices.push({
            step: this.currentStep,
            choice: optionIndex,
            points: option.points
        });

        this.showFeedback(option);
    }

    // Show feedback for choice
    showFeedback(option) {
        const feedbackHTML = `
            <div class="feedback-overlay">
                <div class="feedback-content ${option.correct ? 'correct' : 'incorrect'}">
                    <div class="feedback-icon">
                        <i class="fas fa-${option.correct ? 'check-circle' : 'times-circle'}"></i>
                    </div>
                    <h3>${option.correct ? 'إجابة صحيحة!' : 'يمكن تحسين الإجابة'}</h3>
                    <p>${option.feedback}</p>
                    <div class="points-earned">
                        <i class="fas fa-star"></i>
                        حصلت على ${option.points} نقطة
                    </div>
                    <button class="btn btn-primary" onclick="scenarioManager.nextStep()">
                        ${this.currentStep < this.currentScenario.steps.length - 1 ? 'الخطوة التالية' : 'إنهاء السيناريو'}
                    </button>
                </div>
            </div>
        `;

        document.querySelector('.scenario-container').insertAdjacentHTML('beforeend', feedbackHTML);
    }

    // Move to next step
    nextStep() {
        // Remove feedback overlay
        const feedbackOverlay = document.querySelector('.feedback-overlay');
        if (feedbackOverlay) {
            feedbackOverlay.remove();
        }

        this.currentStep++;
        
        if (this.currentStep >= this.currentScenario.steps.length) {
            this.completeScenario();
        } else {
            // Update the scenario interface for the next step
            this.updateScenarioInterface();
        }
    }

    // Update scenario interface for new step
    updateScenarioInterface() {
        const step = this.currentScenario.steps[this.currentStep];
        
        // Update progress
        document.querySelector('.scenario-progress span').textContent = 
            `الخطوة ${this.currentStep + 1} من ${this.currentScenario.steps.length}`;
        document.querySelector('.progress-fill').style.width = 
            `${((this.currentStep + 1) / this.currentScenario.steps.length) * 100}%`;
        
        // Update step content
        document.querySelector('.step-header h3').textContent = step.title;
        document.querySelector('.step-header p').textContent = step.description;
        document.querySelector('.step-content').innerHTML = this.renderStepContent(step);
    }

    // Complete scenario
    completeScenario() {
        const totalPoints = this.userChoices.reduce((sum, choice) => sum + choice.points, 0);
        
        // Add points to user progress
        window.userProgress.points += totalPoints;
        saveUserProgress();
        updateProgressDisplay();

        // Show completion screen
        const completionHTML = `
            <div class="scenario-completion">
                <div class="completion-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <h2>تهانينا! أكملت السيناريو</h2>
                <div class="completion-stats">
                    <div class="stat">
                        <span class="stat-value">${totalPoints}</span>
                        <span class="stat-label">نقطة مكتسبة</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">${this.currentScenario.steps.length}</span>
                        <span class="stat-label">خطوة مكتملة</span>
                    </div>
                </div>
                <div class="completion-actions">
                    <button class="btn btn-primary" onclick="scenarioManager.closeScenario()">
                        العودة للرئيسية
                    </button>
                    <button class="btn btn-secondary" onclick="scenarioManager.restartScenario()">
                        إعادة السيناريو
                    </button>
                </div>
            </div>
        `;

        document.querySelector('.scenario-content').innerHTML = completionHTML;

        // Trigger gamification events
        if (window.gamificationSystem) {
            document.dispatchEvent(new CustomEvent('scenarioCompleted', {
                detail: { 
                    scenarioId: this.currentScenario.id,
                    points: totalPoints 
                }
            }));
        }
    }

    // Close scenario
    closeScenario() {
        const overlay = document.querySelector('.scenario-overlay');
        if (overlay) {
            overlay.remove();
        }
        this.currentScenario = null;
        this.currentStep = 0;
        this.userChoices = [];
    }

    // Restart scenario
    restartScenario() {
        const scenarioId = this.currentScenario.id;
        this.closeScenario();
        this.startScenario(scenarioId);
    }

    // Toggle keyword selection
    toggleKeyword(index) {
        const checkboxes = document.querySelectorAll('.keyword-row input[type="checkbox"]');
        const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
        
        document.getElementById('submitKeywords').disabled = checkedCount !== 2;
    }

    // Submit keyword selection
    submitKeywordSelection() {
        const checkboxes = document.querySelectorAll('.keyword-row input[type="checkbox"]');
        const selectedKeywords = [];
        
        checkboxes.forEach((checkbox, index) => {
            if (checkbox.checked) {
                selectedKeywords.push(index);
            }
        });

        // Calculate points based on selection
        const step = this.currentScenario.steps[this.currentStep];
        let points = 0;
        selectedKeywords.forEach(index => {
            if (step.content.keywords[index].recommended) {
                points += 25;
            } else {
                points += 10;
            }
        });

        this.userChoices.push({
            step: this.currentStep,
            choice: selectedKeywords,
            points: points
        });

        const feedback = {
            correct: selectedKeywords.every(index => step.content.keywords[index].recommended),
            feedback: selectedKeywords.every(index => step.content.keywords[index].recommended) ?
                'اختيار ممتاز! ركزت على الكلمات ذات المنافسة المتوسطة والحجم الجيد' :
                'اختيار جيد، لكن يمكن التركيز أكثر على الكلمات ذات المنافسة الأقل',
            points: points
        };

        this.showFeedback(feedback);
    }

    // Submit content optimization
    submitContentOptimization() {
        const title = document.getElementById('pageTitle').value;
        const description = document.getElementById('metaDescription').value;

        if (!title || !description) {
            alert('يرجى ملء جميع الحقول');
            return;
        }

        // Simple scoring based on length and keyword inclusion
        let points = 0;
        if (title.length >= 30 && title.length <= 60) points += 15;
        if (description.length >= 120 && description.length <= 160) points += 15;
        if (title.includes('حذاء') || title.includes('جري')) points += 10;
        if (description.includes('نساء') || description.includes('رياضي')) points += 10;

        this.userChoices.push({
            step: this.currentStep,
            choice: { title, description },
            points: Math.max(points, 20) // Minimum 20 points for effort
        });

        const feedback = {
            correct: points >= 40,
            feedback: points >= 40 ? 
                'ممتاز! عنوان ووصف محسنان بشكل جيد' :
                'جيد، لكن يمكن تحسين الطول واستخدام الكلمات المفتاحية',
            points: Math.max(points, 20)
        };

        this.showFeedback(feedback);
    }

    // Toggle demographic selection
    toggleDemographic(index) {
        // This is handled by the checkbox change event
    }

    // Submit audience selection
    submitAudienceSelection() {
        const checkboxes = document.querySelectorAll('.demographic-card input[type="checkbox"]');
        const selectedDemographics = [];
        
        checkboxes.forEach((checkbox, index) => {
            if (checkbox.checked) {
                selectedDemographics.push(index);
            }
        });

        if (selectedDemographics.length === 0) {
            alert('يرجى اختيار فئة واحدة على الأقل');
            return;
        }

        // Calculate points
        const step = this.currentScenario.steps[this.currentStep];
        let points = selectedDemographics.length * 15;
        
        // Bonus for selecting recommended demographics
        selectedDemographics.forEach(index => {
            if (step.content.demographics[index].recommended) {
                points += 10;
            }
        });

        this.userChoices.push({
            step: this.currentStep,
            choice: selectedDemographics,
            points: points
        });

        const feedback = {
            correct: selectedDemographics.some(index => step.content.demographics[index].recommended),
            feedback: 'اختيار جيد للجمهور المستهدف! التركيز على الفئات المناسبة سيحسن نتائج الحملة',
            points: points
        };

        this.showFeedback(feedback);
    }

    // Add scenario styles
    addScenarioStyles() {
        if (document.getElementById('scenarioStyles')) return;
        
        const style = document.createElement('style');
        style.id = 'scenarioStyles';
        style.textContent = `
            .scenario-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.9);
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }
            
            .scenario-container {
                background: white;
                border-radius: 20px;
                max-width: 900px;
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
                direction: rtl;
                position: relative;
            }
            
            .scenario-header {
                background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
                color: white;
                padding: 30px;
                border-radius: 20px 20px 0 0;
                position: relative;
            }
            
            .close-scenario {
                position: absolute;
                top: 20px;
                left: 20px;
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                cursor: pointer;
                font-size: 1.2rem;
            }
            
            .scenario-info h2 {
                font-size: 1.8rem;
                margin-bottom: 15px;
            }
            
            .scenario-meta {
                display: flex;
                gap: 20px;
                margin-bottom: 20px;
                flex-wrap: wrap;
            }
            
            .scenario-meta span {
                background: rgba(255, 255, 255, 0.2);
                padding: 5px 15px;
                border-radius: 20px;
                font-size: 0.9rem;
                display: flex;
                align-items: center;
                gap: 5px;
            }
            
            .scenario-progress {
                margin-top: 20px;
            }
            
            .scenario-content {
                padding: 40px;
            }
            
            .step-header h3 {
                font-size: 1.5rem;
                margin-bottom: 10px;
                color: var(--gray-800);
            }
            
            .step-header p {
                color: var(--gray-600);
                margin-bottom: 30px;
                line-height: 1.6;
            }
            
            .option-btn {
                display: block;
                width: 100%;
                background: var(--gray-50);
                border: 2px solid var(--gray-200);
                padding: 20px;
                margin-bottom: 15px;
                border-radius: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
                text-align: right;
                font-size: 1rem;
            }
            
            .option-btn:hover {
                border-color: var(--primary-color);
                background: var(--gray-100);
            }
            
            .keywords-table {
                background: var(--gray-50);
                border-radius: 12px;
                overflow: hidden;
                margin: 20px 0;
            }
            
            .table-header {
                background: var(--primary-color);
                color: white;
                padding: 15px;
                display: grid;
                grid-template-columns: 2fr 1fr 1fr 1fr 80px;
                gap: 15px;
                font-weight: 600;
            }
            
            .keyword-row {
                padding: 15px;
                display: grid;
                grid-template-columns: 2fr 1fr 1fr 1fr 80px;
                gap: 15px;
                align-items: center;
                border-bottom: 1px solid var(--gray-200);
            }
            
            .keyword-row:last-child {
                border-bottom: none;
            }
            
            .difficulty.عالية, .competition.شديدة {
                color: var(--danger-color);
                font-weight: 600;
            }
            
            .difficulty.متوسطة, .competition.متوسطة {
                color: var(--accent-color);
                font-weight: 600;
            }
            
            .difficulty.منخفضة, .competition.قليلة {
                color: var(--success-color);
                font-weight: 600;
            }
            
            .checkbox-container {
                display: flex;
                align-items: center;
                gap: 10px;
                cursor: pointer;
            }
            
            .checkmark {
                width: 20px;
                height: 20px;
                border: 2px solid var(--gray-300);
                border-radius: 4px;
                position: relative;
            }
            
            .checkbox-container input:checked + .checkmark {
                background: var(--primary-color);
                border-color: var(--primary-color);
            }
            
            .checkbox-container input:checked + .checkmark::after {
                content: '✓';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-weight: bold;
            }
            
            .product-info {
                background: var(--gray-50);
                padding: 20px;
                border-radius: 12px;
                margin: 20px 0;
            }
            
            .optimization-form {
                margin-top: 30px;
            }
            
            .form-group {
                margin-bottom: 25px;
            }
            
            .form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: var(--gray-800);
            }
            
            .form-group input,
            .form-group textarea {
                width: 100%;
                padding: 12px;
                border: 2px solid var(--gray-200);
                border-radius: 8px;
                font-size: 1rem;
                direction: rtl;
            }
            
            .form-group textarea {
                height: 80px;
                resize: vertical;
            }
            
            .form-group small {
                color: var(--gray-500);
                font-size: 0.85rem;
                margin-top: 5px;
                display: block;
            }
            
            .demographics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }
            
            .demographic-card {
                background: var(--gray-50);
                border: 2px solid var(--gray-200);
                border-radius: 12px;
                padding: 20px;
                transition: all 0.3s ease;
            }
            
            .demographic-card.recommended {
                border-color: var(--success-color);
                background: rgba(34, 197, 94, 0.05);
            }
            
            .demographic-card h4 {
                margin-bottom: 15px;
                color: var(--gray-800);
            }
            
            .demo-details p {
                margin-bottom: 8px;
                color: var(--gray-600);
                font-size: 0.9rem;
            }
            
            .feedback-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 100;
            }
            
            .feedback-content {
                background: white;
                padding: 40px;
                border-radius: 20px;
                text-align: center;
                max-width: 400px;
                margin: 20px;
            }
            
            .feedback-icon {
                font-size: 3rem;
                margin-bottom: 20px;
            }
            
            .feedback-content.correct .feedback-icon {
                color: var(--success-color);
            }
            
            .feedback-content.incorrect .feedback-icon {
                color: var(--accent-color);
            }
            
            .points-earned {
                background: var(--accent-color);
                color: white;
                padding: 10px 20px;
                border-radius: 20px;
                margin: 20px 0;
                display: inline-flex;
                align-items: center;
                gap: 8px;
            }
            
            .scenario-completion {
                text-align: center;
                padding: 40px;
            }
            
            .completion-icon {
                font-size: 4rem;
                color: var(--accent-color);
                margin-bottom: 20px;
            }
            
            .completion-stats {
                display: flex;
                justify-content: center;
                gap: 40px;
                margin: 30px 0;
            }
            
            .stat {
                text-align: center;
            }
            
            .stat-value {
                display: block;
                font-size: 2rem;
                font-weight: bold;
                color: var(--primary-color);
            }
            
            .stat-label {
                color: var(--gray-600);
                font-size: 0.9rem;
            }
            
            .completion-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            @media (max-width: 768px) {
                .scenario-container {
                    margin: 10px;
                    max-height: 95vh;
                }
                
                .scenario-header {
                    padding: 20px;
                }
                
                .scenario-content {
                    padding: 20px;
                }
                
                .table-header,
                .keyword-row {
                    grid-template-columns: 1fr;
                    gap: 10px;
                }
                
                .demographics-grid {
                    grid-template-columns: 1fr;
                }
                
                .completion-stats {
                    flex-direction: column;
                    gap: 20px;
                }
            }
        `;
        
        document.head.appendChild(style);
    }

    // Bind events
    bindEvents() {
        // Global function to start scenarios
        window.startScenario = (scenarioId) => {
            this.startScenario(scenarioId);
        };
    }
}

// Initialize scenario manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.scenarioManager = new ScenarioManager();
});
