const stages = [
    {
        title: "المرحلة 1: تحديد المنتج",
        content: "اختر منتجًا ترغب في تسويقه. مثال: هاتف ذكي جديد.",
        example: "مثال المنتج: هاتف ذكي XPro مع كاميرا عالية الدقة."
    },
    {
        title: "المرحلة 2: تحديد الجمهور المستهدف",
        content: "حدد الفئة التي ترغب في استهدافها. مثال: الشباب من 18 إلى 30 سنة.",
        example: "مثال الجمهور: طلاب الجامعات والموظفون الجدد."
    },
    {
        title: "المرحلة 3: اختيار قناة التسويق",
        content: "اختر القناة المناسبة للتسويق. مثال: وسائل التواصل الاجتماعي.",
        example: "مثال القناة: انستغرام وفيسبوك."
    },
    {
        title: "المرحلة 4: إنشاء إعلان",
        content: "صمم إعلانًا جذابًا للمنتج. مثال: صورة للهاتف مع عرض خاص.",
        example: "مثال الإعلان: اشترِ هاتف XPro الآن واحصل على سماعة هدية!"
    },
    {
        title: "المرحلة 5: قياس النتائج",
        content: "تابع نتائج حملتك التسويقية. مثال: عدد المبيعات أو التفاعل على الإعلان.",
        example: "مثال القياس: 1000 مشاهدة و50 عملية شراء خلال أسبوع."
    }
];

let currentStage = 0;
const startBtn = document.getElementById('startBtn');
const gameArea = document.getElementById('gameArea');

startBtn.onclick = () => {
    startBtn.style.display = 'none';
    showStage();
};

function showStage() {
    if (currentStage < stages.length) {
        const stage = stages[currentStage];
        gameArea.innerHTML = `
            <div class="stage">
                <h2>${stage.title}</h2>
                <p>${stage.content}</p>
                <div class="example"><strong>مثال:</strong> ${stage.example}</div>
                <button id="nextBtn">التالي</button>
            </div>
        `;
        gameArea.style.display = 'block';
        document.getElementById('nextBtn').onclick = () => {
            currentStage++;
            showStage();
        };
    } else {
        gameArea.innerHTML = `<h2>تهانينا! أنهيت جميع مراحل اللعبة التعليمية 🎉</h2>`;
    }
}
