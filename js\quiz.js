// Quiz System for Digital Marketing Education
class QuizSystem {
    constructor() {
        this.quizzes = this.initializeQuizzes();
        this.currentQuiz = null;
        this.currentQuestion = 0;
        this.userAnswers = [];
        this.timeRemaining = 0;
        this.timer = null;
        this.bindEvents();
    }

    // Initialize quiz data
    initializeQuizzes() {
        return {
            seo_basics: {
                id: 'seo_basics',
                title: 'اختبار أساسيات تحسين محركات البحث',
                description: 'اختبر معرفتك بأساسيات SEO والمفاهيم الأساسية',
                category: 'seo',
                timeLimit: 15, // minutes
                passingScore: 70,
                totalPoints: 100,
                questions: [
                    {
                        id: 1,
                        type: 'multiple_choice',
                        question: 'ما هو الهدف الأساسي من تحسين محركات البحث (SEO)؟',
                        options: [
                            'زيادة عدد الزوار المدفوعين للموقع',
                            'تحسين ترتيب الموقع في نتائج البحث الطبيعية',
                            'زيادة عدد الإعلانات على الموقع',
                            'تحسين تصميم الموقع فقط'
                        ],
                        correctAnswer: 1,
                        explanation: 'الهدف الأساسي من SEO هو تحسين ترتيب الموقع في نتائج البحث الطبيعية (غير المدفوعة) لزيادة الزوار المجانيين.',
                        points: 10
                    },
                    {
                        id: 2,
                        type: 'multiple_choice',
                        question: 'أي من هذه العوامل الأكثر أهمية في تحسين محركات البحث؟',
                        options: [
                            'عدد الصور في الموقع',
                            'جودة المحتوى والكلمات المفتاحية',
                            'لون الموقع',
                            'عدد الصفحات في الموقع'
                        ],
                        correctAnswer: 1,
                        explanation: 'جودة المحتوى واستخدام الكلمات المفتاحية المناسبة من أهم عوامل نجاح استراتيجية SEO.',
                        points: 10
                    },
                    {
                        id: 3,
                        type: 'true_false',
                        question: 'الكلمات المفتاحية طويلة الذيل (Long-tail keywords) أسهل في المنافسة من الكلمات القصيرة.',
                        correctAnswer: true,
                        explanation: 'صحيح. الكلمات المفتاحية طويلة الذيل عادة ما تكون أقل منافسة وأكثر تحديداً، مما يجعلها أسهل للترتيب.',
                        points: 10
                    },
                    {
                        id: 4,
                        type: 'multiple_choice',
                        question: 'ما هو الطول المثالي لعنوان الصفحة (Title Tag)؟',
                        options: [
                            '10-20 حرف',
                            '30-60 حرف',
                            '100-150 حرف',
                            'لا يوجد حد أقصى'
                        ],
                        correctAnswer: 1,
                        explanation: 'الطول المثالي لعنوان الصفحة هو 30-60 حرف لضمان ظهوره كاملاً في نتائج البحث.',
                        points: 10
                    },
                    {
                        id: 5,
                        type: 'multiple_select',
                        question: 'أي من هذه الأدوات مفيدة للبحث عن الكلمات المفتاحية؟ (اختر أكثر من إجابة)',
                        options: [
                            'Google Keyword Planner',
                            'SEMrush',
                            'Ahrefs',
                            'Facebook Ads Manager'
                        ],
                        correctAnswers: [0, 1, 2],
                        explanation: 'Google Keyword Planner وSEMrush وAhrefs كلها أدوات ممتازة للبحث عن الكلمات المفتاحية. Facebook Ads Manager مخصص للإعلانات.',
                        points: 15
                    },
                    {
                        id: 6,
                        type: 'multiple_choice',
                        question: 'ما هو الوصف التعريفي (Meta Description)؟',
                        options: [
                            'النص الذي يظهر في أعلى الصفحة',
                            'الوصف القصير الذي يظهر تحت العنوان في نتائج البحث',
                            'النص الموجود في تذييل الموقع',
                            'عنوان الصفحة الرئيسية'
                        ],
                        correctAnswer: 1,
                        explanation: 'الوصف التعريفي هو النص القصير الذي يظهر تحت العنوان في نتائج البحث ويساعد في جذب النقرات.',
                        points: 10
                    },
                    {
                        id: 7,
                        type: 'true_false',
                        question: 'شراء الروابط الخلفية (Backlinks) من مواقع غير موثوقة يمكن أن يضر بترتيب موقعك.',
                        correctAnswer: true,
                        explanation: 'صحيح. الروابط الخلفية من مواقع غير موثوقة أو مشبوهة يمكن أن تؤدي إلى عقوبات من محركات البحث.',
                        points: 10
                    },
                    {
                        id: 8,
                        type: 'multiple_choice',
                        question: 'كم مرة يجب أن تظهر الكلمة المفتاحية في المحتوى؟',
                        options: [
                            'في كل جملة',
                            'مرة واحدة فقط',
                            'بشكل طبيعي ومتوازن (2-3% من النص)',
                            'أكبر عدد ممكن'
                        ],
                        correctAnswer: 2,
                        explanation: 'يجب استخدام الكلمة المفتاحية بشكل طبيعي ومتوازن، عادة 2-3% من إجمالي النص لتجنب الحشو المفرط.',
                        points: 10
                    },
                    {
                        id: 9,
                        type: 'multiple_select',
                        question: 'أي من هذه العوامل تؤثر على سرعة تحميل الموقع؟ (اختر أكثر من إجابة)',
                        options: [
                            'حجم الصور',
                            'جودة الاستضافة',
                            'عدد الإضافات',
                            'لون النصوص'
                        ],
                        correctAnswers: [0, 1, 2],
                        explanation: 'حجم الصور وجودة الاستضافة وعدد الإضافات كلها تؤثر على سرعة التحميل. لون النصوص لا يؤثر على السرعة.',
                        points: 15
                    },
                    {
                        id: 10,
                        type: 'multiple_choice',
                        question: 'ما هو أفضل وقت لرؤية نتائج تحسين محركات البحث؟',
                        options: [
                            'خلال أسبوع',
                            'خلال شهر',
                            '3-6 أشهر',
                            'سنة كاملة'
                        ],
                        correctAnswer: 2,
                        explanation: 'نتائج SEO تحتاج عادة إلى 3-6 أشهر لتظهر بوضوح، حيث تحتاج محركات البحث وقت لفهرسة وتقييم التحسينات.',
                        points: 10
                    }
                ]
            },
            social_media_basics: {
                id: 'social_media_basics',
                title: 'اختبار أساسيات التسويق عبر وسائل التواصل',
                description: 'اختبر معرفتك بأساسيات التسويق عبر منصات التواصل الاجتماعي',
                category: 'social_media',
                timeLimit: 12,
                passingScore: 70,
                totalPoints: 80,
                questions: [
                    {
                        id: 1,
                        type: 'multiple_choice',
                        question: 'أي منصة تواصل اجتماعي الأفضل للتسويق B2B؟',
                        options: [
                            'Instagram',
                            'TikTok',
                            'LinkedIn',
                            'Snapchat'
                        ],
                        correctAnswer: 2,
                        explanation: 'LinkedIn هي المنصة الأفضل للتسويق B2B حيث تركز على الشبكات المهنية والأعمال.',
                        points: 10
                    },
                    {
                        id: 2,
                        type: 'true_false',
                        question: 'نشر المحتوى بكثرة على وسائل التواصل دائماً أفضل من النشر بجودة عالية.',
                        correctAnswer: false,
                        explanation: 'خطأ. الجودة أهم من الكمية في التسويق عبر وسائل التواصل. المحتوى عالي الجودة يحقق تفاعل أفضل.',
                        points: 10
                    }
                ]
            }
        };
    }

    // Start a quiz
    startQuiz(quizId) {
        const quiz = this.quizzes[quizId];
        if (!quiz) {
            console.error('Quiz not found:', quizId);
            return;
        }

        this.currentQuiz = quiz;
        this.currentQuestion = 0;
        this.userAnswers = [];
        this.timeRemaining = quiz.timeLimit * 60; // Convert to seconds
        
        this.showQuizInterface();
        this.startTimer();
    }

    // Show quiz interface
    showQuizInterface() {
        const quiz = this.currentQuiz;
        const question = quiz.questions[this.currentQuestion];

        const quizHTML = `
            <div class="quiz-overlay">
                <div class="quiz-container">
                    <div class="quiz-header">
                        <button class="close-quiz" onclick="quizSystem.closeQuiz()">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="quiz-info">
                            <h2>${quiz.title}</h2>
                            <div class="quiz-meta">
                                <span class="question-counter">السؤال ${this.currentQuestion + 1} من ${quiz.questions.length}</span>
                                <span class="time-remaining" id="timeRemaining">${this.formatTime(this.timeRemaining)}</span>
                            </div>
                        </div>
                        <div class="quiz-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${((this.currentQuestion + 1) / quiz.questions.length) * 100}%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="quiz-content">
                        <div class="question-container">
                            <h3 class="question-text">${question.question}</h3>
                            <div class="question-content">
                                ${this.renderQuestion(question)}
                            </div>
                        </div>
                        
                        <div class="quiz-actions">
                            <button class="btn btn-secondary" onclick="quizSystem.previousQuestion()" ${this.currentQuestion === 0 ? 'disabled' : ''}>
                                <i class="fas fa-arrow-right"></i>
                                السؤال السابق
                            </button>
                            <button class="btn btn-primary" onclick="quizSystem.nextQuestion()" id="nextQuestionBtn">
                                ${this.currentQuestion === quiz.questions.length - 1 ? 'إنهاء الاختبار' : 'السؤال التالي'}
                                <i class="fas fa-arrow-left"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', quizHTML);
        this.addQuizStyles();
    }

    // Render question based on type
    renderQuestion(question) {
        switch (question.type) {
            case 'multiple_choice':
                return this.renderMultipleChoice(question);
            case 'true_false':
                return this.renderTrueFalse(question);
            case 'multiple_select':
                return this.renderMultipleSelect(question);
            default:
                return '<p>نوع السؤال غير مدعوم</p>';
        }
    }

    // Render multiple choice question
    renderMultipleChoice(question) {
        return `
            <div class="question-options">
                ${question.options.map((option, index) => `
                    <label class="option-label">
                        <input type="radio" name="question_${question.id}" value="${index}" onchange="quizSystem.selectAnswer(${index})">
                        <span class="option-text">${option}</span>
                    </label>
                `).join('')}
            </div>
        `;
    }

    // Render true/false question
    renderTrueFalse(question) {
        return `
            <div class="question-options">
                <label class="option-label">
                    <input type="radio" name="question_${question.id}" value="true" onchange="quizSystem.selectAnswer(true)">
                    <span class="option-text">صحيح</span>
                </label>
                <label class="option-label">
                    <input type="radio" name="question_${question.id}" value="false" onchange="quizSystem.selectAnswer(false)">
                    <span class="option-text">خطأ</span>
                </label>
            </div>
        `;
    }

    // Render multiple select question
    renderMultipleSelect(question) {
        return `
            <div class="question-options multiple-select">
                <p class="instruction">اختر جميع الإجابات الصحيحة:</p>
                ${question.options.map((option, index) => `
                    <label class="option-label checkbox-option">
                        <input type="checkbox" value="${index}" onchange="quizSystem.toggleAnswer(${index})">
                        <span class="option-text">${option}</span>
                    </label>
                `).join('')}
            </div>
        `;
    }

    // Select answer for single choice questions
    selectAnswer(answer) {
        this.userAnswers[this.currentQuestion] = answer;
        document.getElementById('nextQuestionBtn').disabled = false;
    }

    // Toggle answer for multiple select questions
    toggleAnswer(answerIndex) {
        if (!this.userAnswers[this.currentQuestion]) {
            this.userAnswers[this.currentQuestion] = [];
        }
        
        const answers = this.userAnswers[this.currentQuestion];
        const index = answers.indexOf(answerIndex);
        
        if (index > -1) {
            answers.splice(index, 1);
        } else {
            answers.push(answerIndex);
        }
        
        document.getElementById('nextQuestionBtn').disabled = answers.length === 0;
    }

    // Move to next question
    nextQuestion() {
        if (this.currentQuestion === this.currentQuiz.questions.length - 1) {
            this.finishQuiz();
        } else {
            this.currentQuestion++;
            this.updateQuizInterface();
        }
    }

    // Move to previous question
    previousQuestion() {
        if (this.currentQuestion > 0) {
            this.currentQuestion--;
            this.updateQuizInterface();
        }
    }

    // Update quiz interface for new question
    updateQuizInterface() {
        const quiz = this.currentQuiz;
        const question = quiz.questions[this.currentQuestion];
        
        // Update question counter and progress
        document.querySelector('.question-counter').textContent = 
            `السؤال ${this.currentQuestion + 1} من ${quiz.questions.length}`;
        document.querySelector('.progress-fill').style.width = 
            `${((this.currentQuestion + 1) / quiz.questions.length) * 100}%`;
        
        // Update question content
        document.querySelector('.question-text').textContent = question.question;
        document.querySelector('.question-content').innerHTML = this.renderQuestion(question);
        
        // Update navigation buttons
        const prevBtn = document.querySelector('.quiz-actions .btn-secondary');
        const nextBtn = document.querySelector('.quiz-actions .btn-primary');
        
        prevBtn.disabled = this.currentQuestion === 0;
        nextBtn.textContent = this.currentQuestion === quiz.questions.length - 1 ? 'إنهاء الاختبار' : 'السؤال التالي';
        nextBtn.disabled = !this.userAnswers[this.currentQuestion];
        
        // Restore previous answers if any
        this.restorePreviousAnswers();
    }

    // Restore previous answers for current question
    restorePreviousAnswers() {
        const answer = this.userAnswers[this.currentQuestion];
        if (answer === undefined) return;

        const question = this.currentQuiz.questions[this.currentQuestion];
        
        if (question.type === 'multiple_select') {
            answer.forEach(answerIndex => {
                const checkbox = document.querySelector(`input[type="checkbox"][value="${answerIndex}"]`);
                if (checkbox) checkbox.checked = true;
            });
        } else {
            const radio = document.querySelector(`input[value="${answer}"]`);
            if (radio) radio.checked = true;
        }
        
        document.getElementById('nextQuestionBtn').disabled = false;
    }

    // Start timer
    startTimer() {
        this.timer = setInterval(() => {
            this.timeRemaining--;
            document.getElementById('timeRemaining').textContent = this.formatTime(this.timeRemaining);
            
            if (this.timeRemaining <= 0) {
                this.finishQuiz();
            }
        }, 1000);
    }

    // Format time display
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    // Finish quiz and show results
    finishQuiz() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        const results = this.calculateResults();
        this.showResults(results);
        
        // Save results and update progress
        this.saveQuizResults(results);
    }

    // Calculate quiz results
    calculateResults() {
        const quiz = this.currentQuiz;
        let totalPoints = 0;
        let earnedPoints = 0;
        const questionResults = [];

        quiz.questions.forEach((question, index) => {
            totalPoints += question.points;
            const userAnswer = this.userAnswers[index];
            let isCorrect = false;
            let pointsEarned = 0;

            if (question.type === 'multiple_select') {
                const correctAnswers = question.correctAnswers.sort();
                const userAnswers = (userAnswer || []).sort();
                isCorrect = JSON.stringify(correctAnswers) === JSON.stringify(userAnswers);
            } else {
                isCorrect = userAnswer === question.correctAnswer;
            }

            if (isCorrect) {
                pointsEarned = question.points;
                earnedPoints += pointsEarned;
            }

            questionResults.push({
                question: question,
                userAnswer: userAnswer,
                isCorrect: isCorrect,
                pointsEarned: pointsEarned
            });
        });

        const percentage = Math.round((earnedPoints / totalPoints) * 100);
        const passed = percentage >= quiz.passingScore;

        return {
            quiz: quiz,
            questionResults: questionResults,
            totalPoints: totalPoints,
            earnedPoints: earnedPoints,
            percentage: percentage,
            passed: passed,
            timeUsed: (quiz.timeLimit * 60) - this.timeRemaining
        };
    }

    // Show quiz results
    showResults(results) {
        const resultsHTML = `
            <div class="quiz-results">
                <div class="results-header">
                    <div class="result-icon ${results.passed ? 'passed' : 'failed'}">
                        <i class="fas fa-${results.passed ? 'trophy' : 'times-circle'}"></i>
                    </div>
                    <h2>${results.passed ? 'تهانينا! نجحت في الاختبار' : 'لم تحقق الدرجة المطلوبة'}</h2>
                    <div class="score-display">
                        <span class="score">${results.percentage}%</span>
                        <span class="score-details">${results.earnedPoints} من ${results.totalPoints} نقطة</span>
                    </div>
                </div>
                
                <div class="results-stats">
                    <div class="stat-item">
                        <i class="fas fa-check-circle"></i>
                        <span>الإجابات الصحيحة: ${results.questionResults.filter(r => r.isCorrect).length}</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-times-circle"></i>
                        <span>الإجابات الخاطئة: ${results.questionResults.filter(r => !r.isCorrect).length}</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>الوقت المستغرق: ${this.formatTime(results.timeUsed)}</span>
                    </div>
                </div>
                
                <div class="results-actions">
                    <button class="btn btn-primary" onclick="quizSystem.showDetailedResults()">
                        عرض الإجابات التفصيلية
                    </button>
                    <button class="btn btn-secondary" onclick="quizSystem.retakeQuiz()">
                        إعادة الاختبار
                    </button>
                    <button class="btn btn-outline" onclick="quizSystem.closeQuiz()">
                        العودة للرئيسية
                    </button>
                </div>
            </div>
        `;

        document.querySelector('.quiz-content').innerHTML = resultsHTML;
    }

    // Show detailed results
    showDetailedResults() {
        // Implementation for detailed results view
        alert('عرض الإجابات التفصيلية - قيد التطوير');
    }

    // Retake quiz
    retakeQuiz() {
        const quizId = this.currentQuiz.id;
        this.closeQuiz();
        this.startQuiz(quizId);
    }

    // Save quiz results
    saveQuizResults(results) {
        if (results.passed && window.userProgress) {
            window.userProgress.points += results.earnedPoints;
            
            // Mark quiz as completed
            if (!window.userProgress.completedQuizzes) {
                window.userProgress.completedQuizzes = [];
            }
            
            if (!window.userProgress.completedQuizzes.includes(results.quiz.id)) {
                window.userProgress.completedQuizzes.push(results.quiz.id);
            }
            
            saveUserProgress();
            updateProgressDisplay();
            
            // Trigger gamification events
            if (window.gamificationSystem) {
                document.dispatchEvent(new CustomEvent('quizCompleted', {
                    detail: { 
                        quizId: results.quiz.id,
                        score: results.percentage,
                        points: results.earnedPoints
                    }
                }));
            }
        }
    }

    // Close quiz
    closeQuiz() {
        const overlay = document.querySelector('.quiz-overlay');
        if (overlay) {
            overlay.remove();
        }
        
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        
        this.currentQuiz = null;
        this.currentQuestion = 0;
        this.userAnswers = [];
    }

    // Add quiz styles
    addQuizStyles() {
        if (document.getElementById('quizStyles')) return;
        
        const style = document.createElement('style');
        style.id = 'quizStyles';
        style.textContent = `
            .quiz-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.9);
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }
            
            .quiz-container {
                background: white;
                border-radius: 20px;
                max-width: 800px;
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
                direction: rtl;
                position: relative;
            }
            
            .quiz-header {
                background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
                color: white;
                padding: 30px;
                border-radius: 20px 20px 0 0;
                position: relative;
            }
            
            .close-quiz {
                position: absolute;
                top: 20px;
                left: 20px;
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                cursor: pointer;
                font-size: 1.2rem;
            }
            
            .quiz-info h2 {
                font-size: 1.5rem;
                margin-bottom: 15px;
            }
            
            .quiz-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                flex-wrap: wrap;
                gap: 15px;
            }
            
            .time-remaining {
                background: rgba(255, 255, 255, 0.2);
                padding: 8px 15px;
                border-radius: 20px;
                font-weight: 600;
                font-size: 1.1rem;
            }
            
            .quiz-content {
                padding: 40px;
            }
            
            .question-container {
                margin-bottom: 40px;
            }
            
            .question-text {
                font-size: 1.3rem;
                font-weight: 600;
                margin-bottom: 25px;
                color: var(--gray-800);
                line-height: 1.6;
            }
            
            .question-options {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }
            
            .option-label {
                display: flex;
                align-items: center;
                gap: 15px;
                padding: 20px;
                background: var(--gray-50);
                border: 2px solid var(--gray-200);
                border-radius: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .option-label:hover {
                border-color: var(--primary-color);
                background: var(--gray-100);
            }
            
            .option-label input {
                margin: 0;
            }
            
            .option-text {
                flex: 1;
                font-size: 1rem;
                line-height: 1.5;
            }
            
            .instruction {
                font-weight: 600;
                color: var(--primary-color);
                margin-bottom: 15px;
            }
            
            .quiz-actions {
                display: flex;
                justify-content: space-between;
                gap: 15px;
                padding-top: 30px;
                border-top: 1px solid var(--gray-200);
            }
            
            .quiz-actions .btn {
                flex: 1;
                max-width: 200px;
            }
            
            .quiz-results {
                text-align: center;
                padding: 40px;
            }
            
            .results-header {
                margin-bottom: 40px;
            }
            
            .result-icon {
                font-size: 4rem;
                margin-bottom: 20px;
            }
            
            .result-icon.passed {
                color: var(--success-color);
            }
            
            .result-icon.failed {
                color: var(--danger-color);
            }
            
            .score-display {
                margin: 20px 0;
            }
            
            .score {
                display: block;
                font-size: 3rem;
                font-weight: bold;
                color: var(--primary-color);
            }
            
            .score-details {
                color: var(--gray-600);
                font-size: 1.1rem;
            }
            
            .results-stats {
                display: flex;
                justify-content: center;
                gap: 30px;
                margin-bottom: 40px;
                flex-wrap: wrap;
            }
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 8px;
                color: var(--gray-600);
            }
            
            .results-actions {
                display: flex;
                justify-content: center;
                gap: 15px;
                flex-wrap: wrap;
            }
            
            @media (max-width: 768px) {
                .quiz-container {
                    margin: 10px;
                    max-height: 95vh;
                }
                
                .quiz-header {
                    padding: 20px;
                }
                
                .quiz-content {
                    padding: 20px;
                }
                
                .quiz-meta {
                    flex-direction: column;
                    align-items: flex-start;
                }
                
                .quiz-actions {
                    flex-direction: column;
                }
                
                .quiz-actions .btn {
                    max-width: none;
                }
                
                .results-stats {
                    flex-direction: column;
                    gap: 15px;
                }
                
                .results-actions {
                    flex-direction: column;
                }
            }
        `;
        
        document.head.appendChild(style);
    }

    // Bind events
    bindEvents() {
        // Global function to start quizzes
        window.startQuiz = (quizId) => {
            this.startQuiz(quizId);
        };
    }
}

// Initialize quiz system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.quizSystem = new QuizSystem();
});
