// Visual Effects and Animations Manager
class VisualEffectsManager {
    constructor() {
        this.observers = [];
        this.particles = [];
        this.notifications = [];
        this.init();
    }

    // Initialize visual effects
    init() {
        this.setupScrollAnimations();
        this.setupParticleSystem();
        this.setupCounterAnimations();
        this.setupProgressAnimations();
        this.setupHoverEffects();
        this.bindEvents();
    }

    // Setup scroll-triggered animations
    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                    
                    // Add staggered animation delays for child elements
                    const children = entry.target.querySelectorAll('.animate-on-scroll');
                    children.forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('animate-fade-in-up');
                        }, index * 100);
                    });
                }
            });
        }, observerOptions);

        // Observe elements with scroll-reveal class
        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });

        this.observers.push(observer);
    }

    // Setup particle system for backgrounds
    setupParticleSystem() {
        const particleContainers = document.querySelectorAll('.particles-container');
        
        particleContainers.forEach(container => {
            this.createParticles(container, 15);
        });
    }

    // Create floating particles
    createParticles(container, count) {
        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random positioning and animation
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
            
            // Random colors
            const colors = ['var(--primary-color)', 'var(--secondary-color)', 'var(--accent-color)'];
            particle.style.background = colors[Math.floor(Math.random() * colors.length)];
            
            container.appendChild(particle);
            this.particles.push(particle);
        }
    }

    // Setup counter animations
    setupCounterAnimations() {
        const counters = document.querySelectorAll('.counter');
        
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    counterObserver.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => {
            counterObserver.observe(counter);
        });

        this.observers.push(counterObserver);
    }

    // Animate counter numbers
    animateCounter(element) {
        const target = parseInt(element.getAttribute('data-target') || element.textContent);
        const duration = parseInt(element.getAttribute('data-duration') || '2000');
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);

        element.classList.add('counter-animated');
    }

    // Setup progress bar animations
    setupProgressAnimations() {
        const progressBars = document.querySelectorAll('.progress-bar');
        
        const progressObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateProgressBar(entry.target);
                    progressObserver.unobserve(entry.target);
                }
            });
        });

        progressBars.forEach(bar => {
            progressObserver.observe(bar);
        });

        this.observers.push(progressObserver);
    }

    // Animate progress bars
    animateProgressBar(progressBar) {
        const fill = progressBar.querySelector('.progress-fill');
        if (!fill) return;

        const targetWidth = fill.getAttribute('data-width') || fill.style.width;
        fill.style.setProperty('--target-width', targetWidth);
        progressBar.classList.add('progress-bar-animated');
    }

    // Setup hover effects
    setupHoverEffects() {
        // Add ripple effect to buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', this.createRippleEffect.bind(this));
        });

        // Add magnetic effect to cards
        document.querySelectorAll('.magnetic-card').forEach(card => {
            card.addEventListener('mousemove', this.magneticEffect.bind(this));
            card.addEventListener('mouseleave', this.resetMagneticEffect.bind(this));
        });
    }

    // Create ripple effect on button click
    createRippleEffect(e) {
        const button = e.currentTarget;
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        // Add ripple styles if not already added
        if (!document.getElementById('rippleStyles')) {
            const style = document.createElement('style');
            style.id = 'rippleStyles';
            style.textContent = `
                .btn {
                    position: relative;
                    overflow: hidden;
                }
                .ripple {
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.6);
                    transform: scale(0);
                    animation: ripple-animation 0.6s linear;
                    pointer-events: none;
                }
                @keyframes ripple-animation {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // Magnetic effect for cards
    magneticEffect(e) {
        const card = e.currentTarget;
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;

        const moveX = x * 0.1;
        const moveY = y * 0.1;

        card.style.transform = `translate(${moveX}px, ${moveY}px) scale(1.02)`;
    }

    // Reset magnetic effect
    resetMagneticEffect(e) {
        const card = e.currentTarget;
        card.style.transform = 'translate(0px, 0px) scale(1)';
    }

    // Show achievement notification
    showAchievementNotification(achievement) {
        const notification = document.createElement('div');
        notification.className = 'achievement-notification';
        notification.innerHTML = `
            <div class="achievement-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="achievement-content">
                <h4>إنجاز جديد!</h4>
                <p>${achievement.title}</p>
                <span class="achievement-points">+${achievement.points} نقطة</span>
            </div>
            <button class="close-notification" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add notification styles if not already added
        if (!document.getElementById('notificationStyles')) {
            const style = document.createElement('style');
            style.id = 'notificationStyles';
            style.textContent = `
                .achievement-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, var(--success-color), var(--primary-color));
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    max-width: 350px;
                    z-index: 3000;
                    animation: slideInFromRight 0.5s ease-out;
                }
                .achievement-icon {
                    font-size: 2rem;
                    color: var(--accent-color);
                }
                .achievement-content h4 {
                    margin: 0 0 5px 0;
                    font-size: 1.1rem;
                }
                .achievement-content p {
                    margin: 0 0 5px 0;
                    opacity: 0.9;
                }
                .achievement-points {
                    background: rgba(255, 255, 255, 0.2);
                    padding: 2px 8px;
                    border-radius: 10px;
                    font-size: 0.9rem;
                    font-weight: 600;
                }
                .close-notification {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    padding: 5px;
                    margin-right: -5px;
                }
                @media (max-width: 768px) {
                    .achievement-notification {
                        right: 10px;
                        left: 10px;
                        max-width: none;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);
        this.notifications.push(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideInFromRight 0.5s ease-out reverse';
                setTimeout(() => notification.remove(), 500);
            }
        }, 5000);
    }

    // Show level up effect
    showLevelUpEffect(newLevel) {
        const overlay = document.createElement('div');
        overlay.className = 'level-up-overlay';
        overlay.innerHTML = `
            <div class="level-up-content">
                <div class="level-up-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h2>مستوى جديد!</h2>
                <div class="level-display">
                    <span class="level-number">${newLevel}</span>
                </div>
                <p>تهانينا! وصلت إلى المستوى ${newLevel}</p>
                <button class="btn btn-primary" onclick="this.parentElement.parentElement.remove()">
                    متابعة
                </button>
            </div>
        `;

        // Add level up styles if not already added
        if (!document.getElementById('levelUpStyles')) {
            const style = document.createElement('style');
            style.id = 'levelUpStyles';
            style.textContent = `
                .level-up-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 4000;
                    animation: fadeIn 0.5s ease-out;
                }
                .level-up-content {
                    background: white;
                    padding: 40px;
                    border-radius: 20px;
                    text-align: center;
                    max-width: 400px;
                    margin: 20px;
                    animation: scaleIn 0.5s ease-out;
                }
                .level-up-icon {
                    font-size: 4rem;
                    color: var(--accent-color);
                    margin-bottom: 20px;
                    animation: bounce 1s ease-in-out;
                }
                .level-up-content h2 {
                    color: var(--primary-color);
                    margin-bottom: 20px;
                }
                .level-display {
                    width: 80px;
                    height: 80px;
                    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 20px auto;
                    animation: glow 2s ease-in-out infinite;
                }
                .level-number {
                    color: white;
                    font-size: 2rem;
                    font-weight: bold;
                }
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(overlay);

        // Add confetti effect
        this.createConfetti();
    }

    // Create confetti effect
    createConfetti() {
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
        const confettiCount = 50;

        for (let i = 0; i < confettiCount; i++) {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.left = Math.random() * 100 + 'vw';
            confetti.style.top = '-10px';
            confetti.style.zIndex = '5000';
            confetti.style.pointerEvents = 'none';
            confetti.style.borderRadius = '50%';

            document.body.appendChild(confetti);

            // Animate confetti falling
            const animation = confetti.animate([
                { transform: 'translateY(-10px) rotate(0deg)', opacity: 1 },
                { transform: `translateY(100vh) rotate(${Math.random() * 360}deg)`, opacity: 0 }
            ], {
                duration: Math.random() * 3000 + 2000,
                easing: 'cubic-bezier(0.5, 0, 0.5, 1)'
            });

            animation.onfinish = () => confetti.remove();
        }
    }

    // Add typewriter effect to text
    addTypewriterEffect(element, text, speed = 50) {
        element.textContent = '';
        element.style.borderRight = '2px solid var(--primary-color)';
        
        let i = 0;
        const timer = setInterval(() => {
            element.textContent += text.charAt(i);
            i++;
            
            if (i >= text.length) {
                clearInterval(timer);
                // Remove cursor after typing is complete
                setTimeout(() => {
                    element.style.borderRight = 'none';
                }, 1000);
            }
        }, speed);
    }

    // Cleanup observers and effects
    cleanup() {
        this.observers.forEach(observer => observer.disconnect());
        this.particles.forEach(particle => particle.remove());
        this.notifications.forEach(notification => notification.remove());
        
        this.observers = [];
        this.particles = [];
        this.notifications = [];
    }

    // Bind events
    bindEvents() {
        // Listen for gamification events
        document.addEventListener('achievementUnlocked', (e) => {
            this.showAchievementNotification(e.detail);
        });

        document.addEventListener('levelUp', (e) => {
            this.showLevelUpEffect(e.detail.level);
        });

        // Add scroll reveal class to elements that should animate on scroll
        document.addEventListener('DOMContentLoaded', () => {
            const elementsToReveal = document.querySelectorAll(
                '.module-card, .achievement-card, .lesson-card, .progress-card, .scenario-card'
            );
            
            elementsToReveal.forEach(el => {
                el.classList.add('scroll-reveal');
            });
        });
    }
}

// Initialize visual effects when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.visualEffects = new VisualEffectsManager();
    
    // Add particles container to hero sections
    const heroSections = document.querySelectorAll('.hero, .module-hero');
    heroSections.forEach(hero => {
        if (!hero.querySelector('.particles-container')) {
            const particlesContainer = document.createElement('div');
            particlesContainer.className = 'particles-container';
            hero.appendChild(particlesContainer);
        }
    });
});
