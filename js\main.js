// Main JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
    
    // Mobile navigation
    initializeMobileNav();
    
    // Smooth scrolling for navigation links
    initializeSmoothScrolling();
    
    // Initialize animations
    initializeAnimations();
});

// Initialize the application
function initializeApp() {
    console.log('تم تحميل أكاديمية التسويق الرقمي');
    
    // Load user progress from localStorage
    loadUserProgress();
    
    // Update UI with current progress
    updateProgressDisplay();
    
    // Initialize achievements
    initializeAchievements();
}

// Mobile navigation functionality
function initializeMobileNav() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function() {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const progressHeaderHeight = document.querySelector('.progress-header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight - progressHeaderHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Initialize animations
function initializeAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.module-card, .timeline-item, .achievement-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Load user progress from localStorage
function loadUserProgress() {
    const savedProgress = localStorage.getItem('digitalMarketingProgress');
    if (savedProgress) {
        try {
            window.userProgress = JSON.parse(savedProgress);
        } catch (e) {
            console.error('خطأ في تحميل التقدم المحفوظ:', e);
            window.userProgress = getDefaultProgress();
        }
    } else {
        window.userProgress = getDefaultProgress();
    }
}

// Get default progress structure
function getDefaultProgress() {
    return {
        level: 1,
        points: 0,
        totalProgress: 0,
        modules: {
            seo: { completed: 0, total: 10, unlocked: true },
            social: { completed: 0, total: 8, unlocked: false },
            email: { completed: 0, total: 6, unlocked: false },
            content: { completed: 0, total: 7, unlocked: false },
            ppc: { completed: 0, total: 9, unlocked: false },
            analytics: { completed: 0, total: 5, unlocked: false }
        },
        achievements: [],
        lastActivity: new Date().toISOString()
    };
}

// Save user progress to localStorage
function saveUserProgress() {
    try {
        localStorage.setItem('digitalMarketingProgress', JSON.stringify(window.userProgress));
    } catch (e) {
        console.error('خطأ في حفظ التقدم:', e);
    }
}

// Update progress display in UI
function updateProgressDisplay() {
    const progress = window.userProgress;
    
    // Update level
    const levelElement = document.getElementById('userLevel');
    if (levelElement) {
        levelElement.textContent = progress.level;
    }
    
    // Update points
    const pointsElement = document.getElementById('userPoints');
    if (pointsElement) {
        pointsElement.textContent = progress.points.toLocaleString('ar-SA');
    }
    
    // Update progress bar
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    if (progressFill && progressText) {
        progressFill.style.width = `${progress.totalProgress}%`;
        progressText.textContent = `${Math.round(progress.totalProgress)}%`;
    }
    
    // Update module progress
    updateModuleProgress();
}

// Update module progress displays
function updateModuleProgress() {
    const progress = window.userProgress;
    
    Object.keys(progress.modules).forEach(moduleKey => {
        const moduleData = progress.modules[moduleKey];
        const moduleCard = document.querySelector(`[data-module="${moduleKey}"]`);
        
        if (moduleCard) {
            const progressBar = moduleCard.querySelector('.progress-fill');
            const progressText = moduleCard.querySelector('.progress-text');
            const moduleBtn = moduleCard.querySelector('.module-btn');
            
            if (progressBar && progressText) {
                const percentage = (moduleData.completed / moduleData.total) * 100;
                progressBar.style.width = `${percentage}%`;
                progressText.textContent = `${moduleData.completed}/${moduleData.total} دروس`;
            }
            
            if (moduleBtn) {
                if (!moduleData.unlocked) {
                    moduleBtn.disabled = true;
                    moduleBtn.textContent = 'مقفل';
                    moduleCard.style.opacity = '0.6';
                } else if (moduleData.completed === moduleData.total) {
                    moduleBtn.textContent = 'مكتمل';
                    moduleBtn.style.background = 'var(--success-color)';
                } else {
                    moduleBtn.textContent = moduleData.completed > 0 ? 'متابعة التعلم' : 'ابدأ التعلم';
                }
            }
        }
    });
}

// Initialize achievements display
function initializeAchievements() {
    const achievementsGrid = document.getElementById('achievementsGrid');
    if (!achievementsGrid) return;
    
    const achievements = [
        {
            id: 'first_lesson',
            title: 'الخطوة الأولى',
            description: 'أكمل أول درس لك',
            icon: 'fas fa-baby',
            requirement: 1
        },
        {
            id: 'module_master',
            title: 'سيد الوحدة',
            description: 'أكمل وحدة كاملة',
            icon: 'fas fa-graduation-cap',
            requirement: 'complete_module'
        },
        {
            id: 'quiz_champion',
            title: 'بطل الاختبارات',
            description: 'احصل على 100% في 5 اختبارات',
            icon: 'fas fa-trophy',
            requirement: 'perfect_quizzes'
        },
        {
            id: 'streak_master',
            title: 'سيد الاستمرارية',
            description: 'تعلم لمدة 7 أيام متتالية',
            icon: 'fas fa-fire',
            requirement: 'streak_7'
        },
        {
            id: 'point_collector',
            title: 'جامع النقاط',
            description: 'احصل على 1000 نقطة',
            icon: 'fas fa-star',
            requirement: 1000
        },
        {
            id: 'marketing_expert',
            title: 'خبير التسويق',
            description: 'أكمل جميع الوحدات',
            icon: 'fas fa-crown',
            requirement: 'complete_all'
        }
    ];
    
    achievementsGrid.innerHTML = '';
    
    achievements.forEach(achievement => {
        const isUnlocked = window.userProgress.achievements.includes(achievement.id);
        
        const achievementCard = document.createElement('div');
        achievementCard.className = `achievement-card ${isUnlocked ? 'unlocked' : ''}`;
        
        achievementCard.innerHTML = `
            <div class="achievement-icon">
                <i class="${achievement.icon}"></i>
            </div>
            <h3 class="achievement-title">${achievement.title}</h3>
            <p class="achievement-description">${achievement.description}</p>
        `;
        
        achievementsGrid.appendChild(achievementCard);
    });
}

// Start learning function
function startLearning() {
    // Scroll to modules section
    const modulesSection = document.getElementById('modules');
    if (modulesSection) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const progressHeaderHeight = document.querySelector('.progress-header').offsetHeight;
        const targetPosition = modulesSection.offsetTop - headerHeight - progressHeaderHeight;
        
        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        });
    }
    
    // Add some visual feedback
    setTimeout(() => {
        document.querySelectorAll('.module-card').forEach((card, index) => {
            setTimeout(() => {
                card.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    card.style.transform = 'scale(1)';
                }, 200);
            }, index * 100);
        });
    }, 500);
}

// Show demo function
function showDemo() {
    alert('العرض التوضيحي قريباً! ستتمكن من مشاهدة كيفية عمل المنصة التعليمية.');
}

// Open module function
function openModule(moduleKey) {
    const progress = window.userProgress;
    const moduleData = progress.modules[moduleKey];
    
    if (!moduleData.unlocked) {
        alert('هذه الوحدة مقفلة. أكمل الوحدات السابقة لفتحها.');
        return;
    }
    
    // For now, we'll show an alert. Later this will navigate to the module page
    const moduleNames = {
        seo: 'تحسين محركات البحث',
        social: 'التسويق عبر وسائل التواصل',
        email: 'التسويق عبر البريد الإلكتروني',
        content: 'تسويق المحتوى',
        ppc: 'الإعلانات المدفوعة',
        analytics: 'تحليل البيانات'
    };
    
    const moduleName = moduleNames[moduleKey] || moduleKey;
    alert(`سيتم فتح وحدة: ${moduleName}\nهذه الميزة قيد التطوير.`);
    
    // Simulate starting a lesson (for demo purposes)
    if (moduleData.completed < moduleData.total) {
        simulateLessonCompletion(moduleKey);
    }
}

// Simulate lesson completion (for demo purposes)
function simulateLessonCompletion(moduleKey) {
    const progress = window.userProgress;
    const moduleData = progress.modules[moduleKey];
    
    // Complete one lesson
    moduleData.completed = Math.min(moduleData.completed + 1, moduleData.total);
    
    // Add points
    const pointsEarned = 50;
    progress.points += pointsEarned;
    
    // Show points animation
    showPointsAnimation(pointsEarned);
    
    // Check for level up
    checkLevelUp();
    
    // Check for achievements
    checkAchievements();
    
    // Update total progress
    updateTotalProgress();
    
    // Save progress
    saveUserProgress();
    
    // Update UI
    updateProgressDisplay();
    
    // Show completion message
    setTimeout(() => {
        alert(`تهانينا! لقد أكملت درساً جديداً وحصلت على ${pointsEarned} نقطة!`);
    }, 1000);
}

// Show points animation
function showPointsAnimation(points) {
    const animation = document.createElement('div');
    animation.className = 'points-animation';
    animation.textContent = `+${points}`;
    animation.style.left = '50%';
    animation.style.top = '50%';
    
    document.body.appendChild(animation);
    
    setTimeout(() => {
        document.body.removeChild(animation);
    }, 2000);
}

// Check for level up
function checkLevelUp() {
    const progress = window.userProgress;
    const pointsPerLevel = 500;
    const newLevel = Math.floor(progress.points / pointsPerLevel) + 1;
    
    if (newLevel > progress.level) {
        progress.level = newLevel;
        showLevelUpNotification(newLevel);
    }
}

// Show level up notification
function showLevelUpNotification(level) {
    const notification = document.createElement('div');
    notification.className = 'level-up-notification';
    notification.innerHTML = `
        <div class="level-up-title">تهانينا!</div>
        <div class="level-up-subtitle">وصلت إلى المستوى ${level}</div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        document.body.removeChild(notification);
    }, 3000);
}

// Check for achievements
function checkAchievements() {
    const progress = window.userProgress;
    const newAchievements = [];
    
    // First lesson achievement
    if (!progress.achievements.includes('first_lesson') && getTotalLessonsCompleted() >= 1) {
        newAchievements.push('first_lesson');
    }
    
    // Point collector achievement
    if (!progress.achievements.includes('point_collector') && progress.points >= 1000) {
        newAchievements.push('point_collector');
    }
    
    // Module master achievement
    if (!progress.achievements.includes('module_master') && hasCompletedAnyModule()) {
        newAchievements.push('module_master');
    }
    
    // Add new achievements
    newAchievements.forEach(achievementId => {
        progress.achievements.push(achievementId);
        showAchievementUnlocked(achievementId);
    });
    
    if (newAchievements.length > 0) {
        setTimeout(() => {
            initializeAchievements();
        }, 1000);
    }
}

// Get total lessons completed
function getTotalLessonsCompleted() {
    const progress = window.userProgress;
    return Object.values(progress.modules).reduce((total, module) => total + module.completed, 0);
}

// Check if any module is completed
function hasCompletedAnyModule() {
    const progress = window.userProgress;
    return Object.values(progress.modules).some(module => module.completed === module.total);
}

// Show achievement unlocked notification
function showAchievementUnlocked(achievementId) {
    // This would show a nice achievement notification
    console.log(`تم فتح إنجاز جديد: ${achievementId}`);
}

// Update total progress
function updateTotalProgress() {
    const progress = window.userProgress;
    const totalLessons = Object.values(progress.modules).reduce((total, module) => total + module.total, 0);
    const completedLessons = getTotalLessonsCompleted();
    progress.totalProgress = (completedLessons / totalLessons) * 100;
}
